export interface NotionPerson {
  id: string
  name: string
  email: string
  phone: string
  about: string
  selfie: string[]
  respondent: string
  submissionTime: string
}

export interface User {
  id: number
  name: string
  email: string | null
  phone: string | null
  about: string | null
  selfie: string | null
  password: string
  created_at: string
  role?: 'detective' | 'liar' | 'normal' | 'admin'
}

export interface LoginRequest {
  name: string
  password: string
}

export interface LoginResponse {
  success: boolean
  user?: User
  message?: string
}
