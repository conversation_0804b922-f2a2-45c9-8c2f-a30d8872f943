{"name": "@notionhq/client", "version": "2.3.0", "description": "A simple and easy to use client for the Notion API", "engines": {"node": ">=12"}, "homepage": "https://developers.notion.com/docs/getting-started", "bugs": {"url": "https://github.com/makenotion/notion-sdk-js/issues"}, "repository": {"type": "git", "url": "https://github.com/makenotion/notion-sdk-js/"}, "keywords": ["notion", "<PERSON><PERSON><PERSON>", "rest", "notion-api"], "main": "./build/src", "types": "./build/src/index.d.ts", "scripts": {"prepare": "npm run build", "prepublishOnly": "npm run checkLoggedIn && npm run lint && npm run test", "build": "tsc", "prettier": "prettier --write .", "lint": "prettier --check . && eslint . --ext .ts && cspell '**/*' ", "test": "jest ./test", "check-links": "git ls-files | grep md$ | xargs -n 1 markdown-link-check", "prebuild": "npm run clean", "clean": "rm -rf ./build", "checkLoggedIn": "./scripts/verifyLoggedIn.sh"}, "author": "", "license": "MIT", "files": ["build/package.json", "build/src/**"], "dependencies": {"@types/node-fetch": "^2.5.10", "node-fetch": "^2.6.1"}, "devDependencies": {"@types/jest": "^28.1.4", "@typescript-eslint/eslint-plugin": "^5.39.0", "@typescript-eslint/parser": "^5.39.0", "cspell": "^5.4.1", "eslint": "^7.24.0", "jest": "^28.1.2", "markdown-link-check": "^3.8.7", "prettier": "^2.8.8", "ts-jest": "^28.0.5", "typescript": "^4.8.4"}}