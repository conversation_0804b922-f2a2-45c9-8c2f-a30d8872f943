{"version": 3, "file": "Client.d.ts", "sourceRoot": "", "sources": ["../../src/Client.ts"], "names": [], "mappings": ";;AAAA,OAAO,KAAK,EAAE,KAAK,EAAE,MAAM,WAAW,CAAA;AACtC,OAAO,EACL,KAAK,MAAM,EACX,QAAQ,EAGT,MAAM,WAAW,CAAA;AAQlB,OAAO,EACL,KAAK,kBAAkB,EACvB,KAAK,gBAAgB,EAErB,KAAK,qBAAqB,EAC1B,KAAK,mBAAmB,EAExB,KAAK,qBAAqB,EAC1B,KAAK,mBAAmB,EAExB,KAAK,6BAA6B,EAClC,KAAK,2BAA2B,EAEhC,KAAK,2BAA2B,EAChC,KAAK,yBAAyB,EAE9B,KAAK,uBAAuB,EAC5B,KAAK,qBAAqB,EAE1B,KAAK,qBAAqB,EAC1B,KAAK,mBAAmB,EAExB,KAAK,uBAAuB,EAC5B,KAAK,qBAAqB,EAE1B,KAAK,wBAAwB,EAC7B,KAAK,sBAAsB,EAE3B,KAAK,wBAAwB,EAC7B,KAAK,sBAAsB,EAE3B,KAAK,oBAAoB,EACzB,KAAK,kBAAkB,EAEvB,KAAK,iBAAiB,EACtB,KAAK,eAAe,EAEpB,KAAK,oBAAoB,EACzB,KAAK,kBAAkB,EAEvB,KAAK,iBAAiB,EACtB,KAAK,eAAe,EAEpB,KAAK,mBAAmB,EACxB,KAAK,iBAAiB,EAEtB,KAAK,gBAAgB,EACrB,KAAK,cAAc,EAEnB,KAAK,iBAAiB,EACtB,KAAK,eAAe,EAEpB,KAAK,yBAAyB,EAC9B,KAAK,uBAAuB,EAE5B,KAAK,uBAAuB,EAC5B,KAAK,qBAAqB,EAE1B,KAAK,sBAAsB,EAC3B,KAAK,oBAAoB,EAEzB,KAAK,kBAAkB,EACvB,KAAK,oBAAoB,EAEzB,KAAK,uBAAuB,EAC5B,KAAK,yBAAyB,EAE9B,KAAK,mBAAmB,EACxB,KAAK,qBAAqB,EAE3B,MAAM,iBAAiB,CAAA;AAMxB,OAAO,KAAK,EAAE,cAAc,EAAE,MAAM,eAAe,CAAA;AAEnD,MAAM,WAAW,aAAa;IAC5B,IAAI,CAAC,EAAE,MAAM,CAAA;IACb,SAAS,CAAC,EAAE,MAAM,CAAA;IAClB,OAAO,CAAC,EAAE,MAAM,CAAA;IAChB,QAAQ,CAAC,EAAE,QAAQ,CAAA;IACnB,MAAM,CAAC,EAAE,MAAM,CAAA;IACf,aAAa,CAAC,EAAE,MAAM,CAAA;IACtB,KAAK,CAAC,EAAE,cAAc,CAAA;IACtB,sCAAsC;IACtC,KAAK,CAAC,EAAE,KAAK,CAAA;CACd;AAED,MAAM,WAAW,iBAAiB;IAChC,IAAI,EAAE,MAAM,CAAA;IACZ,MAAM,EAAE,MAAM,CAAA;IACd,KAAK,CAAC,EAAE,WAAW,CAAA;IACnB,IAAI,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;IAC9B;;;;OAIG;IACH,IAAI,CAAC,EACD,MAAM,GACN;QACE,SAAS,EAAE,MAAM,CAAA;QACjB,aAAa,EAAE,MAAM,CAAA;KACtB,CAAA;CACN;AAED,MAAM,CAAC,OAAO,OAAO,MAAM;;IAWzB,MAAM,CAAC,QAAQ,CAAC,oBAAoB,gBAAe;gBAEhC,OAAO,CAAC,EAAE,aAAa;IAY1C;;;;;;;;OAQG;IACU,OAAO,CAAC,YAAY,EAAE,EACjC,IAAI,EACJ,MAAM,EACN,KAAK,EACL,IAAI,EACJ,IAAI,GACL,EAAE,iBAAiB,GAAG,OAAO,CAAC,YAAY,CAAC;IA6F5C,SAAgB,MAAM;QACpB;;WAEG;yBAEK,SAAS,kBAAkB,CAAC,KACjC,QAAQ,gBAAgB,CAAC;QAU5B;;WAEG;uBAEK,SAAS,qBAAqB,CAAC,KACpC,QAAQ,mBAAmB,CAAC;QAU/B;;WAEG;uBAEK,SAAS,qBAAqB,CAAC,KACpC,QAAQ,mBAAmB,CAAC;;YAU7B;;eAEG;2BAEK,SAAS,6BAA6B,CAAC,KAC5C,QAAQ,2BAA2B,CAAC;YAUvC;;eAEG;yBAEK,SAAS,2BAA2B,CAAC,KAC1C,QAAQ,yBAAyB,CAAC;;MAUxC;IAED,SAAgB,SAAS;QACvB;;;;WAIG;qBAEK,SAAS,uBAAuB,CAAC,KACtC,QAAQ,qBAAqB,CAAC;QAUjC;;WAEG;yBAEK,SAAS,qBAAqB,CAAC,KACpC,QAAQ,mBAAmB,CAAC;QAU/B;;WAEG;sBAEK,SAAS,uBAAuB,CAAC,KACtC,QAAQ,qBAAqB,CAAC;QAUjC;;WAEG;uBAEK,SAAS,wBAAwB,CAAC,KACvC,QAAQ,sBAAsB,CAAC;QAUlC;;WAEG;uBAEK,SAAS,wBAAwB,CAAC,KACvC,QAAQ,sBAAsB,CAAC;MASnC;IAED,SAAgB,KAAK;QACnB;;WAEG;uBAEK,SAAS,oBAAoB,CAAC,KACnC,QAAQ,kBAAkB,CAAC;QAU9B;;WAEG;yBACc,SAAS,iBAAiB,CAAC,KAAG,QAAQ,eAAe,CAAC;QAUvE;;WAEG;uBAEK,SAAS,oBAAoB,CAAC,KACnC,QAAQ,kBAAkB,CAAC;;YAU5B;;eAEG;6BAEK,SAAS,yBAAyB,CAAC,KACxC,QAAQ,uBAAuB,CAAC;;MAUtC;IAED,SAAgB,KAAK;QACnB;;WAEG;yBACc,SAAS,iBAAiB,CAAC,KAAG,QAAQ,eAAe,CAAC;QAUvE;;WAEG;qBACU,SAAS,mBAAmB,CAAC,KAAG,QAAQ,iBAAiB,CAAC;QAUvE;;WAEG;mBACQ,SAAS,iBAAiB,CAAC,KAAG,QAAQ,eAAe,CAAC;MASlE;IAED,SAAgB,QAAQ;QACtB;;WAEG;uBAEK,SAAS,uBAAuB,CAAC,KACtC,QAAQ,qBAAqB,CAAC;QAUjC;;WAEG;qBAEK,SAAS,sBAAsB,CAAC,KACrC,QAAQ,oBAAoB,CAAC;MASjC;IAED;;OAEG;IACI,MAAM,SACL,SAAS,gBAAgB,CAAC,KAC/B,QAAQ,cAAc,CAAC,CAQzB;IAED,SAAgB,KAAK;QACnB;;WAEG;sBAEK,oBAAoB,GAAG;YAC3B,SAAS,EAAE,MAAM,CAAA;YACjB,aAAa,EAAE,MAAM,CAAA;SACtB,KACA,QAAQ,kBAAkB,CAAC;QAY9B;;WAEG;2BAEK,yBAAyB,GAAG;YAChC,SAAS,EAAE,MAAM,CAAA;YACjB,aAAa,EAAE,MAAM,CAAA;SACtB,KACA,QAAQ,uBAAuB,CAAC;QAYnC;;WAEG;uBAEK,qBAAqB,GAAG;YAC5B,SAAS,EAAE,MAAM,CAAA;YACjB,aAAa,EAAE,MAAM,CAAA;SACtB,KACA,QAAQ,mBAAmB,CAAC;MAYhC;IAED;;;;;OAKG;IACH,OAAO,CAAC,GAAG;IAUX;;;;;;;;OAQG;IACH,OAAO,CAAC,aAAa;CAQtB;AAKD,KAAK,MAAM,GAAG,KAAK,GAAG,MAAM,GAAG,OAAO,GAAG,QAAQ,CAAA;AACjD,KAAK,WAAW,GAAG,MAAM,CAAC,MAAM,EAAE,MAAM,GAAG,MAAM,GAAG,MAAM,EAAE,CAAC,GAAG,eAAe,CAAA;AAE/E,KAAK,QAAQ,CAAC,CAAC,IAAI,CAAC,GAAG;IAAE,IAAI,CAAC,EAAE,MAAM,CAAA;CAAE,CAAA"}