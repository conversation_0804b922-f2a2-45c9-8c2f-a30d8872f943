{"version": 3, "file": "fetch-types.js", "sourceRoot": "", "sources": ["../../src/fetch-types.ts"], "names": [], "mappings": "", "sourcesContent": ["import type { Agent } from \"http\"\nimport type { Assert } from \"./type-utils\"\nimport type NodeFetchFn from \"node-fetch\"\nimport type {\n  RequestInfo as NodeFetchRequestInfo,\n  RequestInit as NodeFetchRequestInit,\n  Response as NodeFetchResponse,\n} from \"node-fetch\"\n\n// The `Supported` types should be kept up to date in order to exactly match what we use in the client. This ensures maximal compatibility with other `fetch` implementations.\nexport type SupportedRequestInfo = string\n// We can't assert against the browser or native Node fetch types without complicating the package structure (see #401), so perform a best effort against `node-fetch`, which we use by default.\ntype _assertSupportedInfoIsSubtypeOfNodeFetch = Assert<\n  NodeFetchRequestInfo,\n  SupportedRequestInfo\n>\n\nexport type SupportedRequestInit = {\n  agent?: Agent\n  body?: string\n  headers?: Record<string, string>\n  method?: string\n}\ntype _assertSupportedInitIsSubtypeOfNodeFetch = Assert<\n  NodeFetchRequestInit,\n  SupportedRequestInit\n>\n\nexport type SupportedResponse = {\n  ok: boolean\n  text: () => Promise<string>\n  headers: unknown\n  status: number\n}\ntype _assertSupportedResponseIsSubtypeOfNodeFetch = Assert<\n  SupportedResponse,\n  NodeFetchResponse\n>\n\nexport type SupportedFetch = (\n  url: SupportedRequestInfo,\n  init?: SupportedRequestInit\n) => Promise<SupportedResponse>\ntype _assertSupportedFetchIsSubtypeOfNodeFetch = Assert<\n  SupportedFetch,\n  typeof NodeFetchFn\n>\n"]}