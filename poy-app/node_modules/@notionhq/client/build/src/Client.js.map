{"version": 3, "file": "Client.js", "sourceRoot": "", "sources": ["../../src/Client.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;AACA,uCAKkB;AAClB,qCAKiB;AACjB,mCAA8B;AAC9B,mDAsEwB;AACxB,2CAAkC;AAClC,kDAGwB;AAiCxB,MAAqB,MAAM;IAazB,YAAmB,OAAuB;;QAZ1C,+BAAc;QACd,mCAAmB;QACnB,iCAAe;QACf,oCAAkB;QAClB,oCAAkB;QAClB,wCAAsB;QACtB,gCAAsB;QACtB,gCAAyB;QACzB,oCAAkB;QAwHlB;;WAEG;QAEa,WAAM,GAAG;YACvB;;eAEG;YACH,QAAQ,EAAE,CACR,IAAkC,EACP,EAAE;gBAC7B,OAAO,IAAI,CAAC,OAAO,CAAmB;oBACpC,IAAI,EAAE,wBAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;oBACzB,MAAM,EAAE,wBAAQ,CAAC,MAAM;oBACvB,KAAK,EAAE,IAAA,YAAI,EAAC,IAAI,EAAE,wBAAQ,CAAC,WAAW,CAAC;oBACvC,IAAI,EAAE,IAAA,YAAI,EAAC,IAAI,EAAE,wBAAQ,CAAC,UAAU,CAAC;oBACrC,IAAI,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,IAAI;iBACjB,CAAC,CAAA;YACJ,CAAC;YAED;;eAEG;YACH,MAAM,EAAE,CACN,IAAqC,EACP,EAAE;gBAChC,OAAO,IAAI,CAAC,OAAO,CAAsB;oBACvC,IAAI,EAAE,2BAAW,CAAC,IAAI,CAAC,IAAI,CAAC;oBAC5B,MAAM,EAAE,2BAAW,CAAC,MAAM;oBAC1B,KAAK,EAAE,IAAA,YAAI,EAAC,IAAI,EAAE,2BAAW,CAAC,WAAW,CAAC;oBAC1C,IAAI,EAAE,IAAA,YAAI,EAAC,IAAI,EAAE,2BAAW,CAAC,UAAU,CAAC;oBACxC,IAAI,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,IAAI;iBACjB,CAAC,CAAA;YACJ,CAAC;YAED;;eAEG;YACH,MAAM,EAAE,CACN,IAAqC,EACP,EAAE;gBAChC,OAAO,IAAI,CAAC,OAAO,CAAsB;oBACvC,IAAI,EAAE,2BAAW,CAAC,IAAI,CAAC,IAAI,CAAC;oBAC5B,MAAM,EAAE,2BAAW,CAAC,MAAM;oBAC1B,KAAK,EAAE,IAAA,YAAI,EAAC,IAAI,EAAE,2BAAW,CAAC,WAAW,CAAC;oBAC1C,IAAI,EAAE,IAAA,YAAI,EAAC,IAAI,EAAE,2BAAW,CAAC,UAAU,CAAC;oBACxC,IAAI,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,IAAI;iBACjB,CAAC,CAAA;YACJ,CAAC;YACD,QAAQ,EAAE;gBACR;;mBAEG;gBACH,MAAM,EAAE,CACN,IAA6C,EACP,EAAE;oBACxC,OAAO,IAAI,CAAC,OAAO,CAA8B;wBAC/C,IAAI,EAAE,mCAAmB,CAAC,IAAI,CAAC,IAAI,CAAC;wBACpC,MAAM,EAAE,mCAAmB,CAAC,MAAM;wBAClC,KAAK,EAAE,IAAA,YAAI,EAAC,IAAI,EAAE,mCAAmB,CAAC,WAAW,CAAC;wBAClD,IAAI,EAAE,IAAA,YAAI,EAAC,IAAI,EAAE,mCAAmB,CAAC,UAAU,CAAC;wBAChD,IAAI,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,IAAI;qBACjB,CAAC,CAAA;gBACJ,CAAC;gBAED;;mBAEG;gBACH,IAAI,EAAE,CACJ,IAA2C,EACP,EAAE;oBACtC,OAAO,IAAI,CAAC,OAAO,CAA4B;wBAC7C,IAAI,EAAE,iCAAiB,CAAC,IAAI,CAAC,IAAI,CAAC;wBAClC,MAAM,EAAE,iCAAiB,CAAC,MAAM;wBAChC,KAAK,EAAE,IAAA,YAAI,EAAC,IAAI,EAAE,iCAAiB,CAAC,WAAW,CAAC;wBAChD,IAAI,EAAE,IAAA,YAAI,EAAC,IAAI,EAAE,iCAAiB,CAAC,UAAU,CAAC;wBAC9C,IAAI,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,IAAI;qBACjB,CAAC,CAAA;gBACJ,CAAC;aACF;SACF,CAAA;QAEe,cAAS,GAAG;YAC1B;;;;eAIG;YACH,IAAI,EAAE,CACJ,IAAuC,EACP,EAAE;gBAClC,OAAO,IAAI,CAAC,OAAO,CAAwB;oBACzC,IAAI,EAAE,6BAAa,CAAC,IAAI,EAAE;oBAC1B,MAAM,EAAE,6BAAa,CAAC,MAAM;oBAC5B,KAAK,EAAE,IAAA,YAAI,EAAC,IAAI,EAAE,6BAAa,CAAC,WAAW,CAAC;oBAC5C,IAAI,EAAE,IAAA,YAAI,EAAC,IAAI,EAAE,6BAAa,CAAC,UAAU,CAAC;oBAC1C,IAAI,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,IAAI;iBACjB,CAAC,CAAA;YACJ,CAAC;YAED;;eAEG;YACH,QAAQ,EAAE,CACR,IAAqC,EACP,EAAE;gBAChC,OAAO,IAAI,CAAC,OAAO,CAAsB;oBACvC,IAAI,EAAE,2BAAW,CAAC,IAAI,CAAC,IAAI,CAAC;oBAC5B,MAAM,EAAE,2BAAW,CAAC,MAAM;oBAC1B,KAAK,EAAE,IAAA,YAAI,EAAC,IAAI,EAAE,2BAAW,CAAC,WAAW,CAAC;oBAC1C,IAAI,EAAE,IAAA,YAAI,EAAC,IAAI,EAAE,2BAAW,CAAC,UAAU,CAAC;oBACxC,IAAI,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,IAAI;iBACjB,CAAC,CAAA;YACJ,CAAC;YAED;;eAEG;YACH,KAAK,EAAE,CACL,IAAuC,EACP,EAAE;gBAClC,OAAO,IAAI,CAAC,OAAO,CAAwB;oBACzC,IAAI,EAAE,6BAAa,CAAC,IAAI,CAAC,IAAI,CAAC;oBAC9B,MAAM,EAAE,6BAAa,CAAC,MAAM;oBAC5B,KAAK,EAAE,IAAA,YAAI,EAAC,IAAI,EAAE,6BAAa,CAAC,WAAW,CAAC;oBAC5C,IAAI,EAAE,IAAA,YAAI,EAAC,IAAI,EAAE,6BAAa,CAAC,UAAU,CAAC;oBAC1C,IAAI,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,IAAI;iBACjB,CAAC,CAAA;YACJ,CAAC;YAED;;eAEG;YACH,MAAM,EAAE,CACN,IAAwC,EACP,EAAE;gBACnC,OAAO,IAAI,CAAC,OAAO,CAAyB;oBAC1C,IAAI,EAAE,8BAAc,CAAC,IAAI,EAAE;oBAC3B,MAAM,EAAE,8BAAc,CAAC,MAAM;oBAC7B,KAAK,EAAE,IAAA,YAAI,EAAC,IAAI,EAAE,8BAAc,CAAC,WAAW,CAAC;oBAC7C,IAAI,EAAE,IAAA,YAAI,EAAC,IAAI,EAAE,8BAAc,CAAC,UAAU,CAAC;oBAC3C,IAAI,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,IAAI;iBACjB,CAAC,CAAA;YACJ,CAAC;YAED;;eAEG;YACH,MAAM,EAAE,CACN,IAAwC,EACP,EAAE;gBACnC,OAAO,IAAI,CAAC,OAAO,CAAyB;oBAC1C,IAAI,EAAE,8BAAc,CAAC,IAAI,CAAC,IAAI,CAAC;oBAC/B,MAAM,EAAE,8BAAc,CAAC,MAAM;oBAC7B,KAAK,EAAE,IAAA,YAAI,EAAC,IAAI,EAAE,8BAAc,CAAC,WAAW,CAAC;oBAC7C,IAAI,EAAE,IAAA,YAAI,EAAC,IAAI,EAAE,8BAAc,CAAC,UAAU,CAAC;oBAC3C,IAAI,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,IAAI;iBACjB,CAAC,CAAA;YACJ,CAAC;SACF,CAAA;QAEe,UAAK,GAAG;YACtB;;eAEG;YACH,MAAM,EAAE,CACN,IAAoC,EACP,EAAE;gBAC/B,OAAO,IAAI,CAAC,OAAO,CAAqB;oBACtC,IAAI,EAAE,0BAAU,CAAC,IAAI,EAAE;oBACvB,MAAM,EAAE,0BAAU,CAAC,MAAM;oBACzB,KAAK,EAAE,IAAA,YAAI,EAAC,IAAI,EAAE,0BAAU,CAAC,WAAW,CAAC;oBACzC,IAAI,EAAE,IAAA,YAAI,EAAC,IAAI,EAAE,0BAAU,CAAC,UAAU,CAAC;oBACvC,IAAI,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,IAAI;iBACjB,CAAC,CAAA;YACJ,CAAC;YAED;;eAEG;YACH,QAAQ,EAAE,CAAC,IAAiC,EAA4B,EAAE;gBACxE,OAAO,IAAI,CAAC,OAAO,CAAkB;oBACnC,IAAI,EAAE,uBAAO,CAAC,IAAI,CAAC,IAAI,CAAC;oBACxB,MAAM,EAAE,uBAAO,CAAC,MAAM;oBACtB,KAAK,EAAE,IAAA,YAAI,EAAC,IAAI,EAAE,uBAAO,CAAC,WAAW,CAAC;oBACtC,IAAI,EAAE,IAAA,YAAI,EAAC,IAAI,EAAE,uBAAO,CAAC,UAAU,CAAC;oBACpC,IAAI,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,IAAI;iBACjB,CAAC,CAAA;YACJ,CAAC;YAED;;eAEG;YACH,MAAM,EAAE,CACN,IAAoC,EACP,EAAE;gBAC/B,OAAO,IAAI,CAAC,OAAO,CAAqB;oBACtC,IAAI,EAAE,0BAAU,CAAC,IAAI,CAAC,IAAI,CAAC;oBAC3B,MAAM,EAAE,0BAAU,CAAC,MAAM;oBACzB,KAAK,EAAE,IAAA,YAAI,EAAC,IAAI,EAAE,0BAAU,CAAC,WAAW,CAAC;oBACzC,IAAI,EAAE,IAAA,YAAI,EAAC,IAAI,EAAE,0BAAU,CAAC,UAAU,CAAC;oBACvC,IAAI,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,IAAI;iBACjB,CAAC,CAAA;YACJ,CAAC;YACD,UAAU,EAAE;gBACV;;mBAEG;gBACH,QAAQ,EAAE,CACR,IAAyC,EACP,EAAE;oBACpC,OAAO,IAAI,CAAC,OAAO,CAA0B;wBAC3C,IAAI,EAAE,+BAAe,CAAC,IAAI,CAAC,IAAI,CAAC;wBAChC,MAAM,EAAE,+BAAe,CAAC,MAAM;wBAC9B,KAAK,EAAE,IAAA,YAAI,EAAC,IAAI,EAAE,+BAAe,CAAC,WAAW,CAAC;wBAC9C,IAAI,EAAE,IAAA,YAAI,EAAC,IAAI,EAAE,+BAAe,CAAC,UAAU,CAAC;wBAC5C,IAAI,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,IAAI;qBACjB,CAAC,CAAA;gBACJ,CAAC;aACF;SACF,CAAA;QAEe,UAAK,GAAG;YACtB;;eAEG;YACH,QAAQ,EAAE,CAAC,IAAiC,EAA4B,EAAE;gBACxE,OAAO,IAAI,CAAC,OAAO,CAAkB;oBACnC,IAAI,EAAE,uBAAO,CAAC,IAAI,CAAC,IAAI,CAAC;oBACxB,MAAM,EAAE,uBAAO,CAAC,MAAM;oBACtB,KAAK,EAAE,IAAA,YAAI,EAAC,IAAI,EAAE,uBAAO,CAAC,WAAW,CAAC;oBACtC,IAAI,EAAE,IAAA,YAAI,EAAC,IAAI,EAAE,uBAAO,CAAC,UAAU,CAAC;oBACpC,IAAI,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,IAAI;iBACjB,CAAC,CAAA;YACJ,CAAC;YAED;;eAEG;YACH,IAAI,EAAE,CAAC,IAAmC,EAA8B,EAAE;gBACxE,OAAO,IAAI,CAAC,OAAO,CAAoB;oBACrC,IAAI,EAAE,yBAAS,CAAC,IAAI,EAAE;oBACtB,MAAM,EAAE,yBAAS,CAAC,MAAM;oBACxB,KAAK,EAAE,IAAA,YAAI,EAAC,IAAI,EAAE,yBAAS,CAAC,WAAW,CAAC;oBACxC,IAAI,EAAE,IAAA,YAAI,EAAC,IAAI,EAAE,yBAAS,CAAC,UAAU,CAAC;oBACtC,IAAI,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,IAAI;iBACjB,CAAC,CAAA;YACJ,CAAC;YAED;;eAEG;YACH,EAAE,EAAE,CAAC,IAAiC,EAA4B,EAAE;gBAClE,OAAO,IAAI,CAAC,OAAO,CAAkB;oBACnC,IAAI,EAAE,uBAAO,CAAC,IAAI,EAAE;oBACpB,MAAM,EAAE,uBAAO,CAAC,MAAM;oBACtB,KAAK,EAAE,IAAA,YAAI,EAAC,IAAI,EAAE,uBAAO,CAAC,WAAW,CAAC;oBACtC,IAAI,EAAE,IAAA,YAAI,EAAC,IAAI,EAAE,uBAAO,CAAC,UAAU,CAAC;oBACpC,IAAI,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,IAAI;iBACjB,CAAC,CAAA;YACJ,CAAC;SACF,CAAA;QAEe,aAAQ,GAAG;YACzB;;eAEG;YACH,MAAM,EAAE,CACN,IAAuC,EACP,EAAE;gBAClC,OAAO,IAAI,CAAC,OAAO,CAAwB;oBACzC,IAAI,EAAE,6BAAa,CAAC,IAAI,EAAE;oBAC1B,MAAM,EAAE,6BAAa,CAAC,MAAM;oBAC5B,KAAK,EAAE,IAAA,YAAI,EAAC,IAAI,EAAE,6BAAa,CAAC,WAAW,CAAC;oBAC5C,IAAI,EAAE,IAAA,YAAI,EAAC,IAAI,EAAE,6BAAa,CAAC,UAAU,CAAC;oBAC1C,IAAI,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,IAAI;iBACjB,CAAC,CAAA;YACJ,CAAC;YAED;;eAEG;YACH,IAAI,EAAE,CACJ,IAAsC,EACP,EAAE;gBACjC,OAAO,IAAI,CAAC,OAAO,CAAuB;oBACxC,IAAI,EAAE,4BAAY,CAAC,IAAI,EAAE;oBACzB,MAAM,EAAE,4BAAY,CAAC,MAAM;oBAC3B,KAAK,EAAE,IAAA,YAAI,EAAC,IAAI,EAAE,4BAAY,CAAC,WAAW,CAAC;oBAC3C,IAAI,EAAE,IAAA,YAAI,EAAC,IAAI,EAAE,4BAAY,CAAC,UAAU,CAAC;oBACzC,IAAI,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,IAAI;iBACjB,CAAC,CAAA;YACJ,CAAC;SACF,CAAA;QAED;;WAEG;QACI,WAAM,GAAG,CACd,IAAgC,EACP,EAAE;YAC3B,OAAO,IAAI,CAAC,OAAO,CAAiB;gBAClC,IAAI,EAAE,sBAAM,CAAC,IAAI,EAAE;gBACnB,MAAM,EAAE,sBAAM,CAAC,MAAM;gBACrB,KAAK,EAAE,IAAA,YAAI,EAAC,IAAI,EAAE,sBAAM,CAAC,WAAW,CAAC;gBACrC,IAAI,EAAE,IAAA,YAAI,EAAC,IAAI,EAAE,sBAAM,CAAC,UAAU,CAAC;gBACnC,IAAI,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,IAAI;aACjB,CAAC,CAAA;QACJ,CAAC,CAAA;QAEe,UAAK,GAAG;YACtB;;eAEG;YACH,KAAK,EAAE,CACL,IAGC,EAC4B,EAAE;gBAC/B,OAAO,IAAI,CAAC,OAAO,CAAqB;oBACtC,IAAI,EAAE,0BAAU,CAAC,IAAI,EAAE;oBACvB,MAAM,EAAE,0BAAU,CAAC,MAAM;oBACzB,KAAK,EAAE,IAAA,YAAI,EAAC,IAAI,EAAE,0BAAU,CAAC,WAAW,CAAC;oBACzC,IAAI,EAAE,IAAA,YAAI,EAAC,IAAI,EAAE,0BAAU,CAAC,UAAU,CAAC;oBACvC,IAAI,EAAE;wBACJ,SAAS,EAAE,IAAI,CAAC,SAAS;wBACzB,aAAa,EAAE,IAAI,CAAC,aAAa;qBAClC;iBACF,CAAC,CAAA;YACJ,CAAC;YACD;;eAEG;YACH,UAAU,EAAE,CACV,IAGC,EACiC,EAAE;gBACpC,OAAO,IAAI,CAAC,OAAO,CAA0B;oBAC3C,IAAI,EAAE,+BAAe,CAAC,IAAI,EAAE;oBAC5B,MAAM,EAAE,+BAAe,CAAC,MAAM;oBAC9B,KAAK,EAAE,IAAA,YAAI,EAAC,IAAI,EAAE,+BAAe,CAAC,WAAW,CAAC;oBAC9C,IAAI,EAAE,IAAA,YAAI,EAAC,IAAI,EAAE,+BAAe,CAAC,UAAU,CAAC;oBAC5C,IAAI,EAAE;wBACJ,SAAS,EAAE,IAAI,CAAC,SAAS;wBACzB,aAAa,EAAE,IAAI,CAAC,aAAa;qBAClC;iBACF,CAAC,CAAA;YACJ,CAAC;YACD;;eAEG;YACH,MAAM,EAAE,CACN,IAGC,EAC6B,EAAE;gBAChC,OAAO,IAAI,CAAC,OAAO,CAAsB;oBACvC,IAAI,EAAE,2BAAW,CAAC,IAAI,EAAE;oBACxB,MAAM,EAAE,2BAAW,CAAC,MAAM;oBAC1B,KAAK,EAAE,IAAA,YAAI,EAAC,IAAI,EAAE,2BAAW,CAAC,WAAW,CAAC;oBAC1C,IAAI,EAAE,IAAA,YAAI,EAAC,IAAI,EAAE,2BAAW,CAAC,UAAU,CAAC;oBACxC,IAAI,EAAE;wBACJ,SAAS,EAAE,IAAI,CAAC,SAAS;wBACzB,aAAa,EAAE,IAAI,CAAC,aAAa;qBAClC;iBACF,CAAC,CAAA;YACJ,CAAC;SACF,CAAA;QAteC,uBAAA,IAAI,gBAAS,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,MAAA,CAAA;QAC1B,uBAAA,IAAI,oBAAa,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,QAAQ,mCAAI,kBAAQ,CAAC,IAAI,MAAA,CAAA;QACnD,uBAAA,IAAI,kBAAW,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,MAAM,mCAAI,IAAA,2BAAiB,EAAC,mBAAY,CAAC,MAAA,CAAA;QACjE,uBAAA,IAAI,qBAAc,GAAG,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,OAAO,mCAAI,wBAAwB,MAAM,MAAA,CAAA;QACvE,uBAAA,IAAI,qBAAc,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,SAAS,mCAAI,KAAM,MAAA,CAAA;QAC9C,uBAAA,IAAI,yBAAkB,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,aAAa,mCAAI,MAAM,CAAC,oBAAoB,MAAA,CAAA;QAC3E,uBAAA,IAAI,iBAAU,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,KAAK,mCAAI,oBAAS,MAAA,CAAA;QACzC,uBAAA,IAAI,iBAAU,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,KAAK,MAAA,CAAA;QAC5B,uBAAA,IAAI,qBAAc,mBAAmB,sBAAe,EAAE,MAAA,CAAA;IACxD,CAAC;IAED;;;;;;;;OAQG;IACI,KAAK,CAAC,OAAO,CAAe,EACjC,IAAI,EACJ,MAAM,EACN,KAAK,EACL,IAAI,EACJ,IAAI,GACc;QAClB,IAAI,CAAC,GAAG,CAAC,kBAAQ,CAAC,IAAI,EAAE,eAAe,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAA;QAE1D,gEAAgE;QAChE,MAAM,gBAAgB,GACpB,CAAC,IAAI,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC;YACxC,CAAC,CAAC,SAAS;YACX,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA;QAE1B,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,GAAG,uBAAA,IAAI,yBAAW,GAAG,IAAI,EAAE,CAAC,CAAA;QAChD,IAAI,KAAK,EAAE;YACT,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;gBAChD,IAAI,KAAK,KAAK,SAAS,EAAE;oBACvB,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;wBACxB,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAClB,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,EAAE,kBAAkB,CAAC,GAAG,CAAC,CAAC,CACtD,CAAA;qBACF;yBAAM;wBACL,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAA;qBAC5C;iBACF;aACF;SACF;QAED,+EAA+E;QAC/E,IAAI,mBAA2C,CAAA;QAC/C,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;YAC5B,uEAAuE;YACvE,sEAAsE;YACtE,oDAAoD;YACpD,MAAM,mBAAmB,GAAG,GAAG,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,aAAa,EAAE,CAAA;YACrE,MAAM,iBAAiB,GACrB,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAA;YACrD,mBAAmB,GAAG,EAAE,aAAa,EAAE,SAAS,iBAAiB,EAAE,EAAE,CAAA;SACtE;aAAM;YACL,gEAAgE;YAChE,mBAAmB,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAA;SAC/C;QAED,MAAM,OAAO,GAA2B;YACtC,GAAG,mBAAmB;YACtB,gBAAgB,EAAE,uBAAA,IAAI,6BAAe;YACrC,YAAY,EAAE,uBAAA,IAAI,yBAAW;SAC9B,CAAA;QAED,IAAI,gBAAgB,KAAK,SAAS,EAAE;YAClC,OAAO,CAAC,cAAc,CAAC,GAAG,kBAAkB,CAAA;SAC7C;QACD,IAAI;YACF,MAAM,QAAQ,GAAG,MAAM,4BAAmB,CAAC,kBAAkB,CAC3D,uBAAA,IAAI,qBAAO,MAAX,IAAI,EAAQ,GAAG,CAAC,QAAQ,EAAE,EAAE;gBAC1B,MAAM,EAAE,MAAM,CAAC,WAAW,EAAE;gBAC5B,OAAO;gBACP,IAAI,EAAE,gBAAgB;gBACtB,KAAK,EAAE,uBAAA,IAAI,qBAAO;aACnB,CAAC,EACF,uBAAA,IAAI,yBAAW,CAChB,CAAA;YAED,MAAM,YAAY,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAA;YAC1C,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE;gBAChB,MAAM,IAAA,0BAAiB,EAAC,QAAQ,EAAE,YAAY,CAAC,CAAA;aAChD;YAED,MAAM,YAAY,GAAiB,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAA;YAC3D,IAAI,CAAC,GAAG,CAAC,kBAAQ,CAAC,IAAI,EAAE,iBAAiB,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAA;YAC5D,OAAO,YAAY,CAAA;SACpB;QAAC,OAAO,KAAc,EAAE;YACvB,IAAI,CAAC,IAAA,4BAAmB,EAAC,KAAK,CAAC,EAAE;gBAC/B,MAAM,KAAK,CAAA;aACZ;YAED,qDAAqD;YACrD,IAAI,CAAC,GAAG,CAAC,kBAAQ,CAAC,IAAI,EAAE,cAAc,EAAE;gBACtC,IAAI,EAAE,KAAK,CAAC,IAAI;gBAChB,OAAO,EAAE,KAAK,CAAC,OAAO;aACvB,CAAC,CAAA;YAEF,IAAI,IAAA,4BAAmB,EAAC,KAAK,CAAC,EAAE;gBAC9B,oGAAoG;gBACpG,IAAI,CAAC,GAAG,CAAC,kBAAQ,CAAC,KAAK,EAAE,sBAAsB,EAAE;oBAC/C,IAAI,EAAE,KAAK,CAAC,IAAI;iBACjB,CAAC,CAAA;aACH;YAED,MAAM,KAAK,CAAA;SACZ;IACH,CAAC;IAuXD;;;;;OAKG;IACK,GAAG,CACT,KAAe,EACf,OAAe,EACf,SAAkC;QAElC,IAAI,IAAA,0BAAgB,EAAC,KAAK,CAAC,IAAI,IAAA,0BAAgB,EAAC,uBAAA,IAAI,wBAAU,CAAC,EAAE;YAC/D,uBAAA,IAAI,sBAAQ,MAAZ,IAAI,EAAS,KAAK,EAAE,OAAO,EAAE,SAAS,CAAC,CAAA;SACxC;IACH,CAAC;IAED;;;;;;;;OAQG;IACK,aAAa,CAAC,IAAa;QACjC,MAAM,OAAO,GAA2B,EAAE,CAAA;QAC1C,MAAM,eAAe,GAAG,IAAI,aAAJ,IAAI,cAAJ,IAAI,GAAI,uBAAA,IAAI,oBAAM,CAAA;QAC1C,IAAI,eAAe,KAAK,SAAS,EAAE;YACjC,OAAO,CAAC,eAAe,CAAC,GAAG,UAAU,eAAe,EAAE,CAAA;SACvD;QACD,OAAO,OAAO,CAAA;IAChB,CAAC;;AAthBH,yBAuhBC;;AA5gBiB,2BAAoB,GAAG,YAAY,CAAA", "sourcesContent": ["import type { Agent } from \"node:http\"\nimport {\n  type Logger,\n  LogLevel,\n  logLevelSeverity,\n  makeConsoleLogger,\n} from \"./logging\"\nimport {\n  buildRequestError,\n  isHTTPResponseError,\n  isNotionClientError,\n  RequestTimeoutError,\n} from \"./errors\"\nimport { pick } from \"./utils\"\nimport {\n  type GetBlockParameters,\n  type GetBlockResponse,\n  getBlock,\n  type UpdateBlockParameters,\n  type UpdateBlockResponse,\n  updateBlock,\n  type DeleteBlockParameters,\n  type DeleteBlockResponse,\n  deleteBlock,\n  type AppendBlockChildrenParameters,\n  type AppendBlockChildrenResponse,\n  appendBlockChildren,\n  type ListBlockChildrenParameters,\n  type ListBlockChildrenResponse,\n  listBlockChildren,\n  type ListDatabasesParameters,\n  type ListDatabasesResponse,\n  listDatabases,\n  type GetDatabaseParameters,\n  type GetDatabaseResponse,\n  getDatabase,\n  type QueryDatabaseParameters,\n  type QueryDatabaseResponse,\n  queryDatabase,\n  type CreateDatabaseParameters,\n  type CreateDatabaseResponse,\n  createDatabase,\n  type UpdateDatabaseParameters,\n  type UpdateDatabaseResponse,\n  updateDatabase,\n  type CreatePageParameters,\n  type CreatePageResponse,\n  createPage,\n  type GetPageParameters,\n  type GetPageResponse,\n  getPage,\n  type UpdatePageParameters,\n  type UpdatePageResponse,\n  updatePage,\n  type GetUserParameters,\n  type GetUserResponse,\n  getUser,\n  type ListUsersParameters,\n  type ListUsersResponse,\n  listUsers,\n  type SearchParameters,\n  type SearchResponse,\n  search,\n  type GetSelfParameters,\n  type GetSelfResponse,\n  getSelf,\n  type GetPagePropertyParameters,\n  type GetPagePropertyResponse,\n  getPageProperty,\n  type CreateCommentParameters,\n  type CreateCommentResponse,\n  createComment,\n  type ListCommentsParameters,\n  type ListCommentsResponse,\n  listComments,\n  type OauthTokenResponse,\n  type OauthTokenParameters,\n  oauthToken,\n  type OauthIntrospectResponse,\n  type OauthIntrospectParameters,\n  oauthIntrospect,\n  type OauthRevokeResponse,\n  type OauthRevokeParameters,\n  oauthRevoke,\n} from \"./api-endpoints\"\nimport nodeFetch from \"node-fetch\"\nimport {\n  version as PACKAGE_VERSION,\n  name as PACKAGE_NAME,\n} from \"../package.json\"\nimport type { SupportedFetch } from \"./fetch-types\"\n\nexport interface ClientOptions {\n  auth?: string\n  timeoutMs?: number\n  baseUrl?: string\n  logLevel?: LogLevel\n  logger?: Logger\n  notionVersion?: string\n  fetch?: SupportedFetch\n  /** Silently ignored in the browser */\n  agent?: Agent\n}\n\nexport interface RequestParameters {\n  path: string\n  method: Method\n  query?: QueryParams\n  body?: Record<string, unknown>\n  /**\n   * To authenticate using public API token, `auth` should be passed as a\n   * string. If you are trying to complete OAuth, then `auth` should be an object\n   * containing your integration's client ID and secret.\n   */\n  auth?:\n    | string\n    | {\n        client_id: string\n        client_secret: string\n      }\n}\n\nexport default class Client {\n  #auth?: string\n  #logLevel: LogLevel\n  #logger: Logger\n  #prefixUrl: string\n  #timeoutMs: number\n  #notionVersion: string\n  #fetch: SupportedFetch\n  #agent: Agent | undefined\n  #userAgent: string\n\n  static readonly defaultNotionVersion = \"2022-06-28\"\n\n  public constructor(options?: ClientOptions) {\n    this.#auth = options?.auth\n    this.#logLevel = options?.logLevel ?? LogLevel.WARN\n    this.#logger = options?.logger ?? makeConsoleLogger(PACKAGE_NAME)\n    this.#prefixUrl = `${options?.baseUrl ?? \"https://api.notion.com\"}/v1/`\n    this.#timeoutMs = options?.timeoutMs ?? 60_000\n    this.#notionVersion = options?.notionVersion ?? Client.defaultNotionVersion\n    this.#fetch = options?.fetch ?? nodeFetch\n    this.#agent = options?.agent\n    this.#userAgent = `notionhq-client/${PACKAGE_VERSION}`\n  }\n\n  /**\n   * Sends a request.\n   *\n   * @param path\n   * @param method\n   * @param query\n   * @param body\n   * @returns\n   */\n  public async request<ResponseBody>({\n    path,\n    method,\n    query,\n    body,\n    auth,\n  }: RequestParameters): Promise<ResponseBody> {\n    this.log(LogLevel.INFO, \"request start\", { method, path })\n\n    // If the body is empty, don't send the body in the HTTP request\n    const bodyAsJsonString =\n      !body || Object.entries(body).length === 0\n        ? undefined\n        : JSON.stringify(body)\n\n    const url = new URL(`${this.#prefixUrl}${path}`)\n    if (query) {\n      for (const [key, value] of Object.entries(query)) {\n        if (value !== undefined) {\n          if (Array.isArray(value)) {\n            value.forEach(val =>\n              url.searchParams.append(key, decodeURIComponent(val))\n            )\n          } else {\n            url.searchParams.append(key, String(value))\n          }\n        }\n      }\n    }\n\n    // Allow both client ID / client secret based auth as well as token based auth.\n    let authorizationHeader: Record<string, string>\n    if (typeof auth === \"object\") {\n      // Client ID and secret based auth is **ONLY** supported when using the\n      // `/oauth/token` endpoint. If this is the case, handle formatting the\n      // authorization header as required by `Basic` auth.\n      const unencodedCredential = `${auth.client_id}:${auth.client_secret}`\n      const encodedCredential =\n        Buffer.from(unencodedCredential).toString(\"base64\")\n      authorizationHeader = { authorization: `Basic ${encodedCredential}` }\n    } else {\n      // Otherwise format authorization header as `Bearer` token auth.\n      authorizationHeader = this.authAsHeaders(auth)\n    }\n\n    const headers: Record<string, string> = {\n      ...authorizationHeader,\n      \"Notion-Version\": this.#notionVersion,\n      \"user-agent\": this.#userAgent,\n    }\n\n    if (bodyAsJsonString !== undefined) {\n      headers[\"content-type\"] = \"application/json\"\n    }\n    try {\n      const response = await RequestTimeoutError.rejectAfterTimeout(\n        this.#fetch(url.toString(), {\n          method: method.toUpperCase(),\n          headers,\n          body: bodyAsJsonString,\n          agent: this.#agent,\n        }),\n        this.#timeoutMs\n      )\n\n      const responseText = await response.text()\n      if (!response.ok) {\n        throw buildRequestError(response, responseText)\n      }\n\n      const responseJson: ResponseBody = JSON.parse(responseText)\n      this.log(LogLevel.INFO, \"request success\", { method, path })\n      return responseJson\n    } catch (error: unknown) {\n      if (!isNotionClientError(error)) {\n        throw error\n      }\n\n      // Log the error if it's one of our known error types\n      this.log(LogLevel.WARN, \"request fail\", {\n        code: error.code,\n        message: error.message,\n      })\n\n      if (isHTTPResponseError(error)) {\n        // The response body may contain sensitive information so it is logged separately at the DEBUG level\n        this.log(LogLevel.DEBUG, \"failed response body\", {\n          body: error.body,\n        })\n      }\n\n      throw error\n    }\n  }\n\n  /*\n   * Notion API endpoints\n   */\n\n  public readonly blocks = {\n    /**\n     * Retrieve block\n     */\n    retrieve: (\n      args: WithAuth<GetBlockParameters>\n    ): Promise<GetBlockResponse> => {\n      return this.request<GetBlockResponse>({\n        path: getBlock.path(args),\n        method: getBlock.method,\n        query: pick(args, getBlock.queryParams),\n        body: pick(args, getBlock.bodyParams),\n        auth: args?.auth,\n      })\n    },\n\n    /**\n     * Update block\n     */\n    update: (\n      args: WithAuth<UpdateBlockParameters>\n    ): Promise<UpdateBlockResponse> => {\n      return this.request<UpdateBlockResponse>({\n        path: updateBlock.path(args),\n        method: updateBlock.method,\n        query: pick(args, updateBlock.queryParams),\n        body: pick(args, updateBlock.bodyParams),\n        auth: args?.auth,\n      })\n    },\n\n    /**\n     * Delete block\n     */\n    delete: (\n      args: WithAuth<DeleteBlockParameters>\n    ): Promise<DeleteBlockResponse> => {\n      return this.request<DeleteBlockResponse>({\n        path: deleteBlock.path(args),\n        method: deleteBlock.method,\n        query: pick(args, deleteBlock.queryParams),\n        body: pick(args, deleteBlock.bodyParams),\n        auth: args?.auth,\n      })\n    },\n    children: {\n      /**\n       * Append block children\n       */\n      append: (\n        args: WithAuth<AppendBlockChildrenParameters>\n      ): Promise<AppendBlockChildrenResponse> => {\n        return this.request<AppendBlockChildrenResponse>({\n          path: appendBlockChildren.path(args),\n          method: appendBlockChildren.method,\n          query: pick(args, appendBlockChildren.queryParams),\n          body: pick(args, appendBlockChildren.bodyParams),\n          auth: args?.auth,\n        })\n      },\n\n      /**\n       * Retrieve block children\n       */\n      list: (\n        args: WithAuth<ListBlockChildrenParameters>\n      ): Promise<ListBlockChildrenResponse> => {\n        return this.request<ListBlockChildrenResponse>({\n          path: listBlockChildren.path(args),\n          method: listBlockChildren.method,\n          query: pick(args, listBlockChildren.queryParams),\n          body: pick(args, listBlockChildren.bodyParams),\n          auth: args?.auth,\n        })\n      },\n    },\n  }\n\n  public readonly databases = {\n    /**\n     * List databases\n     *\n     * @deprecated Please use `search`\n     */\n    list: (\n      args: WithAuth<ListDatabasesParameters>\n    ): Promise<ListDatabasesResponse> => {\n      return this.request<ListDatabasesResponse>({\n        path: listDatabases.path(),\n        method: listDatabases.method,\n        query: pick(args, listDatabases.queryParams),\n        body: pick(args, listDatabases.bodyParams),\n        auth: args?.auth,\n      })\n    },\n\n    /**\n     * Retrieve a database\n     */\n    retrieve: (\n      args: WithAuth<GetDatabaseParameters>\n    ): Promise<GetDatabaseResponse> => {\n      return this.request<GetDatabaseResponse>({\n        path: getDatabase.path(args),\n        method: getDatabase.method,\n        query: pick(args, getDatabase.queryParams),\n        body: pick(args, getDatabase.bodyParams),\n        auth: args?.auth,\n      })\n    },\n\n    /**\n     * Query a database\n     */\n    query: (\n      args: WithAuth<QueryDatabaseParameters>\n    ): Promise<QueryDatabaseResponse> => {\n      return this.request<QueryDatabaseResponse>({\n        path: queryDatabase.path(args),\n        method: queryDatabase.method,\n        query: pick(args, queryDatabase.queryParams),\n        body: pick(args, queryDatabase.bodyParams),\n        auth: args?.auth,\n      })\n    },\n\n    /**\n     * Create a database\n     */\n    create: (\n      args: WithAuth<CreateDatabaseParameters>\n    ): Promise<CreateDatabaseResponse> => {\n      return this.request<CreateDatabaseResponse>({\n        path: createDatabase.path(),\n        method: createDatabase.method,\n        query: pick(args, createDatabase.queryParams),\n        body: pick(args, createDatabase.bodyParams),\n        auth: args?.auth,\n      })\n    },\n\n    /**\n     * Update a database\n     */\n    update: (\n      args: WithAuth<UpdateDatabaseParameters>\n    ): Promise<UpdateDatabaseResponse> => {\n      return this.request<UpdateDatabaseResponse>({\n        path: updateDatabase.path(args),\n        method: updateDatabase.method,\n        query: pick(args, updateDatabase.queryParams),\n        body: pick(args, updateDatabase.bodyParams),\n        auth: args?.auth,\n      })\n    },\n  }\n\n  public readonly pages = {\n    /**\n     * Create a page\n     */\n    create: (\n      args: WithAuth<CreatePageParameters>\n    ): Promise<CreatePageResponse> => {\n      return this.request<CreatePageResponse>({\n        path: createPage.path(),\n        method: createPage.method,\n        query: pick(args, createPage.queryParams),\n        body: pick(args, createPage.bodyParams),\n        auth: args?.auth,\n      })\n    },\n\n    /**\n     * Retrieve a page\n     */\n    retrieve: (args: WithAuth<GetPageParameters>): Promise<GetPageResponse> => {\n      return this.request<GetPageResponse>({\n        path: getPage.path(args),\n        method: getPage.method,\n        query: pick(args, getPage.queryParams),\n        body: pick(args, getPage.bodyParams),\n        auth: args?.auth,\n      })\n    },\n\n    /**\n     * Update page properties\n     */\n    update: (\n      args: WithAuth<UpdatePageParameters>\n    ): Promise<UpdatePageResponse> => {\n      return this.request<UpdatePageResponse>({\n        path: updatePage.path(args),\n        method: updatePage.method,\n        query: pick(args, updatePage.queryParams),\n        body: pick(args, updatePage.bodyParams),\n        auth: args?.auth,\n      })\n    },\n    properties: {\n      /**\n       * Retrieve page property\n       */\n      retrieve: (\n        args: WithAuth<GetPagePropertyParameters>\n      ): Promise<GetPagePropertyResponse> => {\n        return this.request<GetPagePropertyResponse>({\n          path: getPageProperty.path(args),\n          method: getPageProperty.method,\n          query: pick(args, getPageProperty.queryParams),\n          body: pick(args, getPageProperty.bodyParams),\n          auth: args?.auth,\n        })\n      },\n    },\n  }\n\n  public readonly users = {\n    /**\n     * Retrieve a user\n     */\n    retrieve: (args: WithAuth<GetUserParameters>): Promise<GetUserResponse> => {\n      return this.request<GetUserResponse>({\n        path: getUser.path(args),\n        method: getUser.method,\n        query: pick(args, getUser.queryParams),\n        body: pick(args, getUser.bodyParams),\n        auth: args?.auth,\n      })\n    },\n\n    /**\n     * List all users\n     */\n    list: (args: WithAuth<ListUsersParameters>): Promise<ListUsersResponse> => {\n      return this.request<ListUsersResponse>({\n        path: listUsers.path(),\n        method: listUsers.method,\n        query: pick(args, listUsers.queryParams),\n        body: pick(args, listUsers.bodyParams),\n        auth: args?.auth,\n      })\n    },\n\n    /**\n     * Get details about bot\n     */\n    me: (args: WithAuth<GetSelfParameters>): Promise<GetSelfResponse> => {\n      return this.request<GetSelfResponse>({\n        path: getSelf.path(),\n        method: getSelf.method,\n        query: pick(args, getSelf.queryParams),\n        body: pick(args, getSelf.bodyParams),\n        auth: args?.auth,\n      })\n    },\n  }\n\n  public readonly comments = {\n    /**\n     * Create a comment\n     */\n    create: (\n      args: WithAuth<CreateCommentParameters>\n    ): Promise<CreateCommentResponse> => {\n      return this.request<CreateCommentResponse>({\n        path: createComment.path(),\n        method: createComment.method,\n        query: pick(args, createComment.queryParams),\n        body: pick(args, createComment.bodyParams),\n        auth: args?.auth,\n      })\n    },\n\n    /**\n     * List comments\n     */\n    list: (\n      args: WithAuth<ListCommentsParameters>\n    ): Promise<ListCommentsResponse> => {\n      return this.request<ListCommentsResponse>({\n        path: listComments.path(),\n        method: listComments.method,\n        query: pick(args, listComments.queryParams),\n        body: pick(args, listComments.bodyParams),\n        auth: args?.auth,\n      })\n    },\n  }\n\n  /**\n   * Search\n   */\n  public search = (\n    args: WithAuth<SearchParameters>\n  ): Promise<SearchResponse> => {\n    return this.request<SearchResponse>({\n      path: search.path(),\n      method: search.method,\n      query: pick(args, search.queryParams),\n      body: pick(args, search.bodyParams),\n      auth: args?.auth,\n    })\n  }\n\n  public readonly oauth = {\n    /**\n     * Get token\n     */\n    token: (\n      args: OauthTokenParameters & {\n        client_id: string\n        client_secret: string\n      }\n    ): Promise<OauthTokenResponse> => {\n      return this.request<OauthTokenResponse>({\n        path: oauthToken.path(),\n        method: oauthToken.method,\n        query: pick(args, oauthToken.queryParams),\n        body: pick(args, oauthToken.bodyParams),\n        auth: {\n          client_id: args.client_id,\n          client_secret: args.client_secret,\n        },\n      })\n    },\n    /**\n     * Introspect token\n     */\n    introspect: (\n      args: OauthIntrospectParameters & {\n        client_id: string\n        client_secret: string\n      }\n    ): Promise<OauthIntrospectResponse> => {\n      return this.request<OauthIntrospectResponse>({\n        path: oauthIntrospect.path(),\n        method: oauthIntrospect.method,\n        query: pick(args, oauthIntrospect.queryParams),\n        body: pick(args, oauthIntrospect.bodyParams),\n        auth: {\n          client_id: args.client_id,\n          client_secret: args.client_secret,\n        },\n      })\n    },\n    /**\n     * Revoke token\n     */\n    revoke: (\n      args: OauthRevokeParameters & {\n        client_id: string\n        client_secret: string\n      }\n    ): Promise<OauthRevokeResponse> => {\n      return this.request<OauthRevokeResponse>({\n        path: oauthRevoke.path(),\n        method: oauthRevoke.method,\n        query: pick(args, oauthRevoke.queryParams),\n        body: pick(args, oauthRevoke.bodyParams),\n        auth: {\n          client_id: args.client_id,\n          client_secret: args.client_secret,\n        },\n      })\n    },\n  }\n\n  /**\n   * Emits a log message to the console.\n   *\n   * @param level The level for this message\n   * @param args Arguments to send to the console\n   */\n  private log(\n    level: LogLevel,\n    message: string,\n    extraInfo: Record<string, unknown>\n  ) {\n    if (logLevelSeverity(level) >= logLevelSeverity(this.#logLevel)) {\n      this.#logger(level, message, extraInfo)\n    }\n  }\n\n  /**\n   * Transforms an API key or access token into a headers object suitable for an HTTP request.\n   *\n   * This method uses the instance's value as the default when the input is undefined. If neither are defined, it returns\n   * an empty object\n   *\n   * @param auth API key or access token\n   * @returns headers key-value object\n   */\n  private authAsHeaders(auth?: string): Record<string, string> {\n    const headers: Record<string, string> = {}\n    const authHeaderValue = auth ?? this.#auth\n    if (authHeaderValue !== undefined) {\n      headers[\"authorization\"] = `Bearer ${authHeaderValue}`\n    }\n    return headers\n  }\n}\n\n/*\n * Type aliases to support the generic request interface.\n */\ntype Method = \"get\" | \"post\" | \"patch\" | \"delete\"\ntype QueryParams = Record<string, string | number | string[]> | URLSearchParams\n\ntype WithAuth<P> = P & { auth?: string }\n"]}