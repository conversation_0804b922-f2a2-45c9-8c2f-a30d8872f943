{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../src/utils.ts"], "names": [], "mappings": ";;;AAAA;;;;;;GAMG;AACH,SAAgB,WAAW,CAAC,KAAY;IACtC,MAAM,IAAI,KAAK,CAAC,wCAAwC,KAAK,EAAE,CAAC,CAAA;AAClE,CAAC;AAFD,kCAEC;AAID,SAAgB,IAAI,CAClB,IAAO,EACP,IAAkB;IAElB,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAG,GAAG,CAAC,CAAC,CAAC,CAAA;IACnD,OAAO,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,CAAA;AACpC,CAAC;AAND,oBAMC;AAED,SAAgB,QAAQ,CAAC,CAAU;IACjC,OAAO,OAAO,CAAC,KAAK,QAAQ,IAAI,CAAC,KAAK,IAAI,CAAA;AAC5C,CAAC;AAFD,4BAEC", "sourcesContent": ["/**\n * Utility for enforcing exhaustiveness checks in the type system.\n *\n * @see https://basarat.gitbook.io/typescript/type-system/discriminated-unions#throw-in-exhaustive-checks\n *\n * @param value The variable with no remaining values\n */\nexport function assertNever(value: never): never {\n  throw new Error(`Unexpected value should never occur: ${value}`)\n}\n\ntype AllKeys<T> = T extends unknown ? keyof T : never\n\nexport function pick<O, K extends AllKeys<O>>(\n  base: O,\n  keys: readonly K[]\n): Pick<O, K> {\n  const entries = keys.map(key => [key, base?.[key]])\n  return Object.fromEntries(entries)\n}\n\nexport function isObject(o: unknown): o is Record<PropertyKey, unknown> {\n  return typeof o === \"object\" && o !== null\n}\n"]}