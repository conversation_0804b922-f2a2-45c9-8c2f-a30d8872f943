"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.isMentionRichTextItemResponse = exports.isEquationRichTextItemResponse = exports.isTextRichTextItemResponse = exports.isFullComment = exports.isFullUser = exports.isFullPageOrDatabase = exports.isFullDatabase = exports.isFullPage = exports.isFullBlock = exports.collectPaginatedAPI = exports.iteratePaginatedAPI = void 0;
/**
 * Returns an async iterator over the results of any paginated Notion API.
 *
 * Example (given a notion Client called `notion`):
 *
 * ```
 * for await (const block of iteratePaginatedAPI(notion.blocks.children.list, {
 *   block_id: parentBlockId,
 * })) {
 *   // Do something with block.
 * }
 * ```
 *
 * @param listFn A bound function on the Notion client that represents a conforming paginated
 *   API. Example: `notion.blocks.children.list`.
 * @param firstPageArgs Arguments that should be passed to the API on the first and subsequent
 *   calls to the API. Any necessary `next_cursor` will be automatically populated by
 *   this function. Example: `{ block_id: "<my block id>" }`
 */
async function* iteratePaginatedAPI(listFn, firstPageArgs) {
    let nextCursor = firstPageArgs.start_cursor;
    do {
        const response = await listFn({
            ...firstPageArgs,
            start_cursor: nextCursor,
        });
        yield* response.results;
        nextCursor = response.next_cursor;
    } while (nextCursor);
}
exports.iteratePaginatedAPI = iteratePaginatedAPI;
/**
 * Collect all of the results of paginating an API into an in-memory array.
 *
 * Example (given a notion Client called `notion`):
 *
 * ```
 * const blocks = await collectPaginatedAPI(notion.blocks.children.list, {
 *   block_id: parentBlockId,
 * })
 * // Do something with blocks.
 * ```
 *
 * @param listFn A bound function on the Notion client that represents a conforming paginated
 *   API. Example: `notion.blocks.children.list`.
 * @param firstPageArgs Arguments that should be passed to the API on the first and subsequent
 *   calls to the API. Any necessary `next_cursor` will be automatically populated by
 *   this function. Example: `{ block_id: "<my block id>" }`
 */
async function collectPaginatedAPI(listFn, firstPageArgs) {
    const results = [];
    for await (const item of iteratePaginatedAPI(listFn, firstPageArgs)) {
        results.push(item);
    }
    return results;
}
exports.collectPaginatedAPI = collectPaginatedAPI;
/**
 * @returns `true` if `response` is a full `BlockObjectResponse`.
 */
function isFullBlock(response) {
    return response.object === "block" && "type" in response;
}
exports.isFullBlock = isFullBlock;
/**
 * @returns `true` if `response` is a full `PageObjectResponse`.
 */
function isFullPage(response) {
    return response.object === "page" && "url" in response;
}
exports.isFullPage = isFullPage;
/**
 * @returns `true` if `response` is a full `DatabaseObjectResponse`.
 */
function isFullDatabase(response) {
    return response.object === "database" && "title" in response;
}
exports.isFullDatabase = isFullDatabase;
/**
 * @returns `true` if `response` is a full `DatabaseObjectResponse` or a full
 * `PageObjectResponse`.
 */
function isFullPageOrDatabase(response) {
    if (response.object === "database") {
        return isFullDatabase(response);
    }
    else {
        return isFullPage(response);
    }
}
exports.isFullPageOrDatabase = isFullPageOrDatabase;
/**
 * @returns `true` if `response` is a full `UserObjectResponse`.
 */
function isFullUser(response) {
    return "type" in response;
}
exports.isFullUser = isFullUser;
/**
 * @returns `true` if `response` is a full `CommentObjectResponse`.
 */
function isFullComment(response) {
    return "created_by" in response;
}
exports.isFullComment = isFullComment;
/**
 * @returns `true` if `richText` is a `TextRichTextItemResponse`.
 */
function isTextRichTextItemResponse(richText) {
    return richText.type === "text";
}
exports.isTextRichTextItemResponse = isTextRichTextItemResponse;
/**
 * @returns `true` if `richText` is an `EquationRichTextItemResponse`.
 */
function isEquationRichTextItemResponse(richText) {
    return richText.type === "equation";
}
exports.isEquationRichTextItemResponse = isEquationRichTextItemResponse;
/**
 * @returns `true` if `richText` is an `MentionRichTextItemResponse`.
 */
function isMentionRichTextItemResponse(richText) {
    return richText.type === "mention";
}
exports.isMentionRichTextItemResponse = isMentionRichTextItemResponse;
//# sourceMappingURL=helpers.js.map