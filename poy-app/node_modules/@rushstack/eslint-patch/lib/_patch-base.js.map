{"version": 3, "file": "_patch-base.js", "sourceRoot": "", "sources": ["../src/_patch-base.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;;;;AAE3D,wEAAwE;AACxE,EAAE;AACF,oHAAoH;AACpH,EAAE;AACF,kEAAkE;AAClE,EAAE;AAEF,gDAAwB;AAExB,MAAM,uBAAuB,GAA6B,CAAC,EAAE,EAAE,EAAE,CAC/D,OAAO,EAAE,KAAK,QAAQ,IAAI,CAAC,CAAC,EAAE,IAAI,MAAM,IAAI,EAAE,IAAK,EAAwB,CAAC,IAAI,KAAK,kBAAkB,CAAC;AAuUxG,0DAAuB;AArUzB,MAAM,iBAAiB,GAAW,gCAAgC,CAAC;AAEnE,MAAM;AACN,wFAAwF;AACxF,MAAM,gBAAgB,GACpB,0GAA0G,CAAC;AAS7G,SAAS,cAAc,CAAC,KAAa;IACnC,MAAM,eAAe,GAA2B,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC7E,IAAI,CAAC,eAAe,EAAE,CAAC;QACrB,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,OAAO;QACL,IAAI,EAAE,eAAe,CAAC,CAAC,CAAC;QACxB,MAAM,EAAE,eAAe,CAAC,CAAC,CAAC;QAC1B,UAAU,EAAE,QAAQ,CAAC,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;QAC5C,MAAM,EAAE,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,SAAS;KAC1E,CAAC;AACJ,CAAC;AAED,SAAS,aAAa;IACpB,MAAM,QAAQ,GAAuB,EAAE,CAAC;IACxC,MAAM,uBAAuB,GAAW,KAAK,CAAC,eAAe,CAAC;IAC9D,KAAK,CAAC,eAAe,GAAG,QAAQ,CAAC;IACjC,KAAK,CAAC,iBAAiB,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAC;IACjD,KAAK,CAAC,eAAe,GAAG,uBAAuB,CAAC;IAChD,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;QACpB,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;IACnD,CAAC;IAED,MAAM,EAAE,KAAK,EAAE,GAAG,QAAQ,CAAC;IAC3B,MAAM,UAAU,GAAa,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAC/C,MAAM,MAAM,GAAsB,EAAE,CAAC;IACrC,KAAK,MAAM,IAAI,IAAI,UAAU,EAAE,CAAC;QAC9B,MAAM,KAAK,GAAgC,cAAc,CAAC,IAAI,CAAC,CAAC;QAChE,IAAI,KAAK,EAAE,CAAC;YACV,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACrB,CAAC;IACH,CAAC;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,+BAA+B;AAC/B,oDAAoD;AACpD,IAAI,kBAAkB,GAAuB,SAAS,CAAC;AAEvD,0CAA0C;AAC1C,2DAA2D;AAC3D,IAAI,sBAAsB,GAAuB,SAAS,CAAC;AAE3D,8CAA8C;AAC9C,sEAAsE;AACtE,IAAI,kBAAkB,GAAuB,SAAS,CAAC;AAEvD,4BAA4B;AAC5B,oDAAoD;AACpD,IAAI,UAAU,GAAuB,SAAS,CAAC;AAE/C,uDAAuD;AACvD,qCAAqC;AACrC,IAAI,YAAY,GAAuB,SAAS,CAAC;AA2P/C,oCAAY;AAzPd,mDAAmD;AACnD,KAAK,IAAI,aAAa,GAAe,MAAM,IAAM,CAAC;IAChD,IAAI,iBAAiB,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,EAAE,CAAC;QACnD,0DAA0D;QAC1D,wDAAwD;QACxD,8CAA8C;QAC9C,MAAM,UAAU,GAAsB,aAAa,EAAE,CAAC;QACtD,MAAM,WAAW,GAAgC,UAAU,CAAC,IAAI,CAC9D,CAAC,KAAsB,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,CAC3E,CAAC;QACF,IAAI,WAAW,EAAE,CAAC;YAChB,yEAAyE;YACzE,IAAI,WAAW,GAAuB,WAAW,CAAC,IAAI,CAAC;YACvD,OAAO,WAAW,EAAE,CAAC;gBACnB,MAAM,aAAa,GAAW,cAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;gBACxD,IAAI,aAAa,KAAK,WAAW,EAAE,CAAC;oBAClC,MAAM;gBACR,CAAC;gBACD,WAAW,GAAG,aAAa,CAAC;gBAC5B,IAAI,CAAC;oBACH,uBAAA,YAAY,GAAG,cAAI,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,qBAAqB,EAAE,EAAE,KAAK,EAAE,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC;oBAC9F,MAAM;gBACR,CAAC;gBAAC,OAAO,EAAW,EAAE,CAAC;oBACrB,IAAI,CAAC,uBAAuB,CAAC,EAAE,CAAC,EAAE,CAAC;wBACjC,MAAM,EAAE,CAAC;oBACX,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAED,IAAI,YAAY,EAAE,CAAC;YACjB,MAAM,kBAAkB,GAAW,cAAI,CAAC,OAAO,CAC7C,OAAO,CAAC,OAAO,CAAC,+BAA+B,EAAE,EAAE,KAAK,EAAE,CAAC,YAAY,CAAC,EAAE,CAAC,CAC5E,CAAC;YACF,kBAAkB,GAAG,cAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,mBAAmB,CAAC,CAAC;QAC1E,CAAC;QAED,MAAM;IACR,CAAC;IAED,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;QAC1B,MAAM;IACR,CAAC;IACD,aAAa,GAAG,aAAa,CAAC,MAAM,CAAC;AACvC,CAAC;AAED,IAAI,CAAC,YAAY,EAAE,CAAC;IAClB,uCAAuC;IACvC,KAAK,IAAI,aAAa,GAAe,MAAM,IAAM,CAAC;QAChD,IAAI,CAAC,kBAAkB,EAAE,CAAC;YACxB,IAAI,aAAa,CAAC,QAAQ,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC;gBACpD,yEAAyE;gBACzE,2CAA2C;gBAC3C,IAAI,CAAC;oBACH,MAAM,kBAAkB,GAAW,cAAI,CAAC,OAAO,CAC7C,OAAO,CAAC,OAAO,CAAC,+BAA+B,EAAE,EAAE,KAAK,EAAE,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC,CAClF,CAAC;oBAEF,6DAA6D;oBAC7D,0CAA0C;oBAC1C,MAAM,0BAA0B,GAAW,cAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,mBAAmB,CAAC,CAAC;oBAC9F,IAAI,0BAA0B,KAAK,aAAa,CAAC,QAAQ,EAAE,CAAC;wBAC1D,kBAAkB,GAAG,0BAA0B,CAAC;oBAClD,CAAC;gBACH,CAAC;gBAAC,OAAO,EAAW,EAAE,CAAC;oBACrB,4DAA4D;oBAC5D,4DAA4D;oBAC5D,iBAAiB;oBACjB,IAAI,CAAC,uBAAuB,CAAC,EAAE,CAAC,EAAE,CAAC;wBACjC,MAAM,EAAE,CAAC;oBACX,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;aAAM,CAAC;YACN,0CAA0C;YAC1C,4CAA4C;YAC5C,IAAI,CAAC;gBACH,MAAM,qBAAqB,GAAW,cAAI,CAAC,OAAO,CAChD,OAAO,CAAC,OAAO,CAAC,qBAAqB,EAAE;oBACrC,KAAK,EAAE,CAAC,aAAa,CAAC,IAAI,CAAC;iBAC5B,CAAC,CACH,CAAC;gBAEF,6DAA6D;gBAC7D,0CAA0C;gBAC1C,IAAI,aAAa,CAAC,QAAQ,CAAC,UAAU,CAAC,qBAAqB,GAAG,cAAI,CAAC,GAAG,CAAC,EAAE,CAAC;oBACxE,uBAAA,YAAY,GAAG,qBAAqB,CAAC;oBACrC,MAAM;gBACR,CAAC;YACH,CAAC;YAAC,OAAO,EAAW,EAAE,CAAC;gBACrB,4DAA4D;gBAC5D,4DAA4D;gBAC5D,iBAAiB;gBACjB,IAAI,CAAC,uBAAuB,CAAC,EAAE,CAAC,EAAE,CAAC;oBACjC,MAAM,EAAE,CAAC;gBACX,CAAC;YACH,CAAC;QACH,CAAC;QAED,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;YAC1B,MAAM;QACR,CAAC;QACD,aAAa,GAAG,aAAa,CAAC,MAAM,CAAC;IACvC,CAAC;AACH,CAAC;AAED,IAAI,CAAC,YAAY,EAAE,CAAC;IAClB,wCAAwC;IACxC,KAAK,IAAI,aAAa,GAAe,MAAM,IAAM,CAAC;QAChD,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAC5B,gEAAgE;YAChE,qDAAqD;YACrD,IAAI,CAAC;gBACH,MAAM,cAAc,GAAW,cAAI,CAAC,OAAO,CACzC,OAAO,CAAC,OAAO,CAAC,+BAA+B,EAAE;oBAC/C,KAAK,EAAE,CAAC,aAAa,CAAC,IAAI,CAAC;iBAC5B,CAAC,CACH,CAAC;gBAEF,MAAM,8BAA8B,GAAW,cAAI,CAAC,IAAI,CACtD,cAAc,EACd,8BAA8B,CAC/B,CAAC;gBACF,IAAI,8BAA8B,KAAK,aAAa,CAAC,QAAQ,EAAE,CAAC;oBAC9D,sBAAsB,GAAG,8BAA8B,CAAC;oBACxD,kBAAkB,GAAG,GAAG,cAAc,sCAAsC,CAAC;oBAC7E,UAAU,GAAG,GAAG,cAAc,oBAAoB,CAAC;gBACrD,CAAC;YACH,CAAC;YAAC,OAAO,EAAW,EAAE,CAAC;gBACrB,4DAA4D;gBAC5D,4DAA4D;gBAC5D,iBAAiB;gBACjB,IAAI,CAAC,uBAAuB,CAAC,EAAE,CAAC,EAAE,CAAC;oBACjC,MAAM,EAAE,CAAC;gBACX,CAAC;YACH,CAAC;QACH,CAAC;aAAM,IAAI,aAAa,CAAC,QAAQ,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE,CAAC;YAC5D,0CAA0C;YAC1C,4CAA4C;YAC5C,IAAI,CAAC;gBACH,MAAM,qBAAqB,GAAW,cAAI,CAAC,OAAO,CAChD,OAAO,CAAC,OAAO,CAAC,qBAAqB,EAAE;oBACrC,KAAK,EAAE,CAAC,aAAa,CAAC,IAAI,CAAC;iBAC5B,CAAC,CACH,CAAC;gBAEF,IAAI,cAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE,8BAA8B,CAAC,KAAK,aAAa,CAAC,QAAQ,EAAE,CAAC;oBAChG,uBAAA,YAAY,GAAG,qBAAqB,CAAC;oBACrC,MAAM;gBACR,CAAC;YACH,CAAC;YAAC,OAAO,EAAW,EAAE,CAAC;gBACrB,4DAA4D;gBAC5D,4DAA4D;gBAC5D,gBAAgB;gBAChB,IAAI,CAAC,uBAAuB,CAAC,EAAE,CAAC,EAAE,CAAC;oBACjC,MAAM,EAAE,CAAC;gBACX,CAAC;YACH,CAAC;QACH,CAAC;QAED,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;YAC1B,MAAM;QACR,CAAC;QACD,aAAa,GAAG,aAAa,CAAC,MAAM,CAAC;IACvC,CAAC;AACH,CAAC;AAED,IAAI,CAAC,YAAY,EAAE,CAAC;IAClB,gCAAgC;IAChC,KAAK,IAAI,aAAa,GAAe,MAAM,IAAM,CAAC;QAChD,gEAAgE;QAChE,sDAAsD;QACtD,IAAI,mEAAmE,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,EAAE,CAAC;YACrG,uBAAA,YAAY,GAAG,cAAI,CAAC,IAAI,CAAC,cAAI,CAAC,OAAO,CAAC,aAAa,CAAC,QAAQ,CAAC,EAAE,OAAO,CAAC,CAAC;YACxE,sBAAsB,GAAG,GAAG,YAAY,sCAAsC,CAAC;YAC/E,kBAAkB,GAAG,GAAG,YAAY,sCAAsC,CAAC;YAE3E,2GAA2G;YAC3G,4GAA4G;YAC5G,IAAI,cAAkC,CAAC;YACvC,IAAI,CAAC;gBACH,cAAc,GAAG,cAAI,CAAC,OAAO,CAC3B,OAAO,CAAC,OAAO,CAAC,+BAA+B,EAAE;oBAC/C,KAAK,EAAE,CAAC,aAAa,CAAC,IAAI,CAAC;iBAC5B,CAAC,CACH,CAAC;YACJ,CAAC;YAAC,OAAO,EAAW,EAAE,CAAC;gBACrB,IAAI,CAAC,uBAAuB,CAAC,EAAE,CAAC,EAAE,CAAC;oBACjC,MAAM,EAAE,CAAC;gBACX,CAAC;YACH,CAAC;YAED,UAAU,GAAG,GAAG,cAAc,aAAd,cAAc,cAAd,cAAc,GAAI,YAAY,oBAAoB,CAAC;YACnE,MAAM;QACR,CAAC;QAED,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;YAC1B,+CAA+C;YAC/C,MAAM,IAAI,KAAK,CACb,yEAAyE;gBACvE,kGAAkG;gBAClG,+CAA+C,CAClD,CAAC;QACJ,CAAC;QACD,aAAa,GAAG,aAAa,CAAC,MAAM,CAAC;IACvC,CAAC;AACH,CAAC;AAED,oCAAoC;AACpC,MAAM,qBAAqB,GAAW,GAAG,YAAY,eAAe,CAAC;AACrE,MAAM,mBAAmB,GAAwB,OAAO,CAAC,qBAAqB,CAAC,CAAC;AACnE,QAAA,oBAAoB,GAAW,mBAAmB,CAAC,OAAO,CAAC;AACxE,MAAM,oBAAoB,GAAW,QAAQ,CAAC,4BAAoB,EAAE,EAAE,CAAC,CAAC;AAyCtE,oDAAoB;AAxCtB,IAAI,KAAK,CAAC,oBAAoB,CAAC,EAAE,CAAC;IAChC,MAAM,IAAI,KAAK,CACb,mCAAmC,4BAAoB,cAAc,qBAAqB,GAAG,CAC9F,CAAC;AACJ,CAAC;AAED,IAAI,CAAC,CAAC,oBAAoB,IAAI,CAAC,IAAI,oBAAoB,IAAI,CAAC,CAAC,EAAE,CAAC;IAC9D,MAAM,IAAI,KAAK,CACb,0FAA0F;QACxF,mBAAmB,4BAAoB,KAAK;QAC5C,sCAAsC;QACtC,+CAA+C,CAClD,CAAC;AACJ,CAAC;AAED,8DAA8D;AAC9D,IAAI,kBAAuB,CAAC;AAC5B,IAAI,oBAAoB,IAAI,CAAC,IAAI,kBAAkB,EAAE,CAAC;IACpD,6BAAA,kBAAkB,GAAG,OAAO,CAAC,kBAAkB,CAAC,CAAC,MAAM,CAAC,kBAAkB,CAAC;AAC7E,CAAC;KAAM,IAAI,sBAAsB,EAAE,CAAC;IAClC,6BAAA,kBAAkB,GAAG,OAAO,CAAC,sBAAsB,CAAC,CAAC,kBAAkB,CAAC;AAC1E,CAAC;AAED,8DAA8D;AAC9D,IAAI,cAAgC,CAAC;AACrC,8DAA8D;AAC9D,IAAI,MAAqC,CAAC;AAC1C,IAAI,oBAAoB,IAAI,CAAC,IAAI,kBAAkB,EAAE,CAAC;IACpD,yBAAA,cAAc,GAAG,OAAO,CAAC,kBAAkB,CAAC,CAAC,MAAM,CAAC,cAAc,CAAC;IACnE,iBAAA,MAAM,GAAG,OAAO,CAAC,kBAAkB,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC;AACrD,CAAC;KAAM,IAAI,kBAAkB,IAAI,UAAU,EAAE,CAAC;IAC5C,yBAAA,cAAc,GAAG,OAAO,CAAC,kBAAkB,CAAC,CAAC;IAC7C,iBAAA,MAAM,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC;AAC/B,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See LICENSE in the project root for license information.\n\n// This is a workaround for https://github.com/eslint/eslint/issues/3458\n//\n// To correct how ESLint searches for plugin packages, add this line to the top of your project's .eslintrc.js file:\n//\n//    require(\"@rushstack/eslint-patch/modern-module-resolution\");\n//\n\nimport path from 'path';\n\nconst isModuleResolutionError: (ex: unknown) => boolean = (ex) =>\n  typeof ex === 'object' && !!ex && 'code' in ex && (ex as { code: unknown }).code === 'MODULE_NOT_FOUND';\n\nconst FLAT_CONFIG_REGEX: RegExp = /eslint\\.config\\.(cjs|mjs|js)$/i;\n\n// Ex:\n//     at async ESLint.lintFiles (C:\\\\path\\\\to\\\\\\\\eslint\\\\lib\\\\eslint\\\\eslint.js:720:21)\nconst NODE_STACK_REGEX: RegExp =\n  /^\\s*at (?:((?:\\[object object\\])?[^\\\\/]+(?: \\[as \\S+\\])?) )?\\(?(.*?)(?::(\\d+)| (\\d+))(?::(\\d+))?\\)?\\s*$/i;\n\ninterface INodeStackFrame {\n  file: string;\n  method?: string;\n  lineNumber: number;\n  column?: number;\n}\n\nfunction parseNodeStack(stack: string): INodeStackFrame | undefined {\n  const stackTraceMatch: RegExpExecArray | null = NODE_STACK_REGEX.exec(stack);\n  if (!stackTraceMatch) {\n    return undefined;\n  }\n\n  return {\n    file: stackTraceMatch[2],\n    method: stackTraceMatch[1],\n    lineNumber: parseInt(stackTraceMatch[3], 10),\n    column: stackTraceMatch[4] ? parseInt(stackTraceMatch[4], 10) : undefined\n  };\n}\n\nfunction getStackTrace(): INodeStackFrame[] {\n  const stackObj: { stack?: string } = {};\n  const originalStackTraceLimit: number = Error.stackTraceLimit;\n  Error.stackTraceLimit = Infinity;\n  Error.captureStackTrace(stackObj, getStackTrace);\n  Error.stackTraceLimit = originalStackTraceLimit;\n  if (!stackObj.stack) {\n    throw new Error('Unable to capture stack trace');\n  }\n\n  const { stack } = stackObj;\n  const stackLines: string[] = stack.split('\\n');\n  const frames: INodeStackFrame[] = [];\n  for (const line of stackLines) {\n    const frame: INodeStackFrame | undefined = parseNodeStack(line);\n    if (frame) {\n      frames.push(frame);\n    }\n  }\n\n  return frames;\n}\n\n// Module path for eslintrc.cjs\n// Example: \".../@eslint/eslintrc/dist/eslintrc.cjs\"\nlet eslintrcBundlePath: string | undefined = undefined;\n\n// Module path for config-array-factory.js\n// Example: \".../@eslint/eslintrc/lib/config-array-factory\"\nlet configArrayFactoryPath: string | undefined = undefined;\n\n// Module path for relative-module-resolver.js\n// Example: \".../@eslint/eslintrc/lib/shared/relative-module-resolver\"\nlet moduleResolverPath: string | undefined = undefined;\n\n// Module path for naming.js\n// Example: \".../@eslint/eslintrc/lib/shared/naming\"\nlet namingPath: string | undefined = undefined;\n\n// Folder path where ESLint's package.json can be found\n// Example: \".../node_modules/eslint\"\nlet eslintFolder: string | undefined = undefined;\n\n// Probe for the ESLint >=9.0.0 flat config layout:\nfor (let currentModule: NodeModule = module; ; ) {\n  if (FLAT_CONFIG_REGEX.test(currentModule.filename)) {\n    // Obtain the stack trace of the current module, since the\n    // parent module of a flat config is undefined. From the\n    // stack trace, we can find the ESLint folder.\n    const stackTrace: INodeStackFrame[] = getStackTrace();\n    const targetFrame: INodeStackFrame | undefined = stackTrace.find(\n      (frame: INodeStackFrame) => frame.file && frame.file.endsWith('eslint.js')\n    );\n    if (targetFrame) {\n      // Walk up the path and continuously attempt to resolve the ESLint folder\n      let currentPath: string | undefined = targetFrame.file;\n      while (currentPath) {\n        const potentialPath: string = path.dirname(currentPath);\n        if (potentialPath === currentPath) {\n          break;\n        }\n        currentPath = potentialPath;\n        try {\n          eslintFolder = path.dirname(require.resolve('eslint/package.json', { paths: [currentPath] }));\n          break;\n        } catch (ex: unknown) {\n          if (!isModuleResolutionError(ex)) {\n            throw ex;\n          }\n        }\n      }\n    }\n\n    if (eslintFolder) {\n      const eslintrcFolderPath: string = path.dirname(\n        require.resolve('@eslint/eslintrc/package.json', { paths: [eslintFolder] })\n      );\n      eslintrcBundlePath = path.join(eslintrcFolderPath, 'dist/eslintrc.cjs');\n    }\n\n    break;\n  }\n\n  if (!currentModule.parent) {\n    break;\n  }\n  currentModule = currentModule.parent;\n}\n\nif (!eslintFolder) {\n  // Probe for the ESLint >=8.0.0 layout:\n  for (let currentModule: NodeModule = module; ; ) {\n    if (!eslintrcBundlePath) {\n      if (currentModule.filename.endsWith('eslintrc.cjs')) {\n        // For ESLint >=8.0.0, all @eslint/eslintrc code is bundled at this path:\n        //   .../@eslint/eslintrc/dist/eslintrc.cjs\n        try {\n          const eslintrcFolderPath: string = path.dirname(\n            require.resolve('@eslint/eslintrc/package.json', { paths: [currentModule.path] })\n          );\n\n          // Make sure we actually resolved the module in our call path\n          // and not some other spurious dependency.\n          const resolvedEslintrcBundlePath: string = path.join(eslintrcFolderPath, 'dist/eslintrc.cjs');\n          if (resolvedEslintrcBundlePath === currentModule.filename) {\n            eslintrcBundlePath = resolvedEslintrcBundlePath;\n          }\n        } catch (ex: unknown) {\n          // Module resolution failures are expected, as we're walking\n          // up our require stack to look for eslint. All other errors\n          // are re-thrown.\n          if (!isModuleResolutionError(ex)) {\n            throw ex;\n          }\n        }\n      }\n    } else {\n      // Next look for a file in ESLint's folder\n      //   .../eslint/lib/cli-engine/cli-engine.js\n      try {\n        const eslintCandidateFolder: string = path.dirname(\n          require.resolve('eslint/package.json', {\n            paths: [currentModule.path]\n          })\n        );\n\n        // Make sure we actually resolved the module in our call path\n        // and not some other spurious dependency.\n        if (currentModule.filename.startsWith(eslintCandidateFolder + path.sep)) {\n          eslintFolder = eslintCandidateFolder;\n          break;\n        }\n      } catch (ex: unknown) {\n        // Module resolution failures are expected, as we're walking\n        // up our require stack to look for eslint. All other errors\n        // are re-thrown.\n        if (!isModuleResolutionError(ex)) {\n          throw ex;\n        }\n      }\n    }\n\n    if (!currentModule.parent) {\n      break;\n    }\n    currentModule = currentModule.parent;\n  }\n}\n\nif (!eslintFolder) {\n  // Probe for the ESLint >=7.12.0 layout:\n  for (let currentModule: NodeModule = module; ; ) {\n    if (!configArrayFactoryPath) {\n      // For ESLint >=7.12.0, config-array-factory.js is at this path:\n      //   .../@eslint/eslintrc/lib/config-array-factory.js\n      try {\n        const eslintrcFolder: string = path.dirname(\n          require.resolve('@eslint/eslintrc/package.json', {\n            paths: [currentModule.path]\n          })\n        );\n\n        const resolvedConfigArrayFactoryPath: string = path.join(\n          eslintrcFolder,\n          '/lib/config-array-factory.js'\n        );\n        if (resolvedConfigArrayFactoryPath === currentModule.filename) {\n          configArrayFactoryPath = resolvedConfigArrayFactoryPath;\n          moduleResolverPath = `${eslintrcFolder}/lib/shared/relative-module-resolver`;\n          namingPath = `${eslintrcFolder}/lib/shared/naming`;\n        }\n      } catch (ex: unknown) {\n        // Module resolution failures are expected, as we're walking\n        // up our require stack to look for eslint. All other errors\n        // are re-thrown.\n        if (!isModuleResolutionError(ex)) {\n          throw ex;\n        }\n      }\n    } else if (currentModule.filename.endsWith('cli-engine.js')) {\n      // Next look for a file in ESLint's folder\n      //   .../eslint/lib/cli-engine/cli-engine.js\n      try {\n        const eslintCandidateFolder: string = path.dirname(\n          require.resolve('eslint/package.json', {\n            paths: [currentModule.path]\n          })\n        );\n\n        if (path.join(eslintCandidateFolder, 'lib/cli-engine/cli-engine.js') === currentModule.filename) {\n          eslintFolder = eslintCandidateFolder;\n          break;\n        }\n      } catch (ex: unknown) {\n        // Module resolution failures are expected, as we're walking\n        // up our require stack to look for eslint. All other errors\n        // are rethrown.\n        if (!isModuleResolutionError(ex)) {\n          throw ex;\n        }\n      }\n    }\n\n    if (!currentModule.parent) {\n      break;\n    }\n    currentModule = currentModule.parent;\n  }\n}\n\nif (!eslintFolder) {\n  // Probe for the <7.12.0 layout:\n  for (let currentModule: NodeModule = module; ; ) {\n    // For ESLint <7.12.0, config-array-factory.js was at this path:\n    //   .../eslint/lib/cli-engine/config-array-factory.js\n    if (/[\\\\/]eslint[\\\\/]lib[\\\\/]cli-engine[\\\\/]config-array-factory\\.js$/i.test(currentModule.filename)) {\n      eslintFolder = path.join(path.dirname(currentModule.filename), '../..');\n      configArrayFactoryPath = `${eslintFolder}/lib/cli-engine/config-array-factory`;\n      moduleResolverPath = `${eslintFolder}/lib/shared/relative-module-resolver`;\n\n      // The naming module was moved to @eslint/eslintrc in ESLint 7.8.0, which is also when the @eslint/eslintrc\n      // package was created and added to ESLint, so we need to probe for whether it's in the old or new location.\n      let eslintrcFolder: string | undefined;\n      try {\n        eslintrcFolder = path.dirname(\n          require.resolve('@eslint/eslintrc/package.json', {\n            paths: [currentModule.path]\n          })\n        );\n      } catch (ex: unknown) {\n        if (!isModuleResolutionError(ex)) {\n          throw ex;\n        }\n      }\n\n      namingPath = `${eslintrcFolder ?? eslintFolder}/lib/shared/naming`;\n      break;\n    }\n\n    if (!currentModule.parent) {\n      // This was tested with ESLint 6.1.0 .. 7.12.1.\n      throw new Error(\n        'Failed to patch ESLint because the calling module was not recognized.\\n' +\n          'If you are using a newer ESLint version that may be unsupported, please create a GitHub issue:\\n' +\n          'https://github.com/microsoft/rushstack/issues'\n      );\n    }\n    currentModule = currentModule.parent;\n  }\n}\n\n// Detect the ESLint package version\nconst eslintPackageJsonPath: string = `${eslintFolder}/package.json`;\nconst eslintPackageObject: { version: string } = require(eslintPackageJsonPath);\nexport const eslintPackageVersion: string = eslintPackageObject.version;\nconst ESLINT_MAJOR_VERSION: number = parseInt(eslintPackageVersion, 10);\nif (isNaN(ESLINT_MAJOR_VERSION)) {\n  throw new Error(\n    `Unable to parse ESLint version \"${eslintPackageVersion}\" in file \"${eslintPackageJsonPath}\"`\n  );\n}\n\nif (!(ESLINT_MAJOR_VERSION >= 6 && ESLINT_MAJOR_VERSION <= 9)) {\n  throw new Error(\n    'The ESLint patch script has only been tested with ESLint version 6.x, 7.x, 8.x, and 9.x.' +\n      ` (Your version: ${eslintPackageVersion})\\n` +\n      'Consider reporting a GitHub issue:\\n' +\n      'https://github.com/microsoft/rushstack/issues'\n  );\n}\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nlet configArrayFactory: any;\nif (ESLINT_MAJOR_VERSION >= 8 && eslintrcBundlePath) {\n  configArrayFactory = require(eslintrcBundlePath).Legacy.ConfigArrayFactory;\n} else if (configArrayFactoryPath) {\n  configArrayFactory = require(configArrayFactoryPath).ConfigArrayFactory;\n}\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nlet ModuleResolver: { resolve: any };\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nlet Naming: { normalizePackageName: any };\nif (ESLINT_MAJOR_VERSION >= 8 && eslintrcBundlePath) {\n  ModuleResolver = require(eslintrcBundlePath).Legacy.ModuleResolver;\n  Naming = require(eslintrcBundlePath).Legacy.naming;\n} else if (moduleResolverPath && namingPath) {\n  ModuleResolver = require(moduleResolverPath);\n  Naming = require(namingPath);\n}\n\nexport {\n  eslintFolder,\n  configArrayFactory,\n  ModuleResolver,\n  Naming,\n  ESLINT_MAJOR_VERSION,\n  isModuleResolutionError\n};\n"]}