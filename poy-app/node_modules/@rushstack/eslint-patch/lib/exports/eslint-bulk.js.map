{"version": 3, "file": "eslint-bulk.js", "sourceRoot": "", "sources": ["../../src/exports/eslint-bulk.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;AAE3D,sGAAsG;AAEtG,iDAA+C", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See LICENSE in the project root for license information.\n\n// \"lib/exports/eslint-bulk\" is the entry point for the @rushstack/eslint-bulk command line front end.\n\nimport '../eslint-bulk-suppressions/cli/start';\n"]}