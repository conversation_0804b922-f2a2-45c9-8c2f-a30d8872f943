"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./app/admin/page.tsx":
/*!****************************!*\
  !*** ./app/admin/page.tsx ***!
  \****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AdminPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction AdminPage() {\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [allUsers, setAllUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [syncing, setSyncing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [copiedPassword, setCopiedPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [notes, setNotes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showNotes, setShowNotes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Check if user is logged in and is admin\n        const userData = localStorage.getItem(\"user\");\n        if (!userData) {\n            router.push(\"/\");\n            return;\n        }\n        const userObj = JSON.parse(userData);\n        if (userObj.role !== \"admin\") {\n            router.push(\"/dashboard\");\n            return;\n        }\n        setUser(userObj);\n        fetchUsers();\n        fetchNotes();\n    }, [\n        router\n    ]);\n    const fetchUsers = async ()=>{\n        try {\n            const response = await fetch(\"/api/admin/users\");\n            const data = await response.json();\n            if (data.success) {\n                setAllUsers(data.users);\n            }\n        } catch (error) {\n            console.error(\"Failed to fetch users:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const fetchNotes = async ()=>{\n        try {\n            const response = await fetch(\"/api/notes\");\n            const data = await response.json();\n            if (data.success) {\n                setNotes(data.notes);\n            }\n        } catch (error) {\n            console.error(\"Failed to fetch notes:\", error);\n        }\n    };\n    const handleSync = async ()=>{\n        setSyncing(true);\n        setMessage(\"\");\n        try {\n            const response = await fetch(\"/api/sync\", {\n                method: \"POST\"\n            });\n            const data = await response.json();\n            if (data.success) {\n                setMessage(data.message);\n                fetchUsers() // Refresh the user list\n                ;\n                fetchNotes() // Refresh notes\n                ;\n            } else {\n                setMessage(data.message || \"Sync failed\");\n            }\n        } catch (error) {\n            setMessage(\"Network error during sync\");\n        } finally{\n            setSyncing(false);\n        }\n    };\n    const handleClearDB = async ()=>{\n        if (!confirm(\"Are you sure you want to clear the database? This will remove all users except admin.\")) {\n            return;\n        }\n        try {\n            const response = await fetch(\"/api/admin/clear\", {\n                method: \"POST\"\n            });\n            const data = await response.json();\n            if (data.success) {\n                setMessage(data.message);\n                fetchUsers() // Refresh the user list\n                ;\n            } else {\n                setMessage(data.message || \"Clear failed\");\n            }\n        } catch (error) {\n            setMessage(\"Network error during clear\");\n        }\n    };\n    const handleLogout = ()=>{\n        localStorage.removeItem(\"user\");\n        router.push(\"/\");\n    };\n    const copyPassword = async (password, userName)=>{\n        try {\n            await navigator.clipboard.writeText(password);\n            setCopiedPassword(userName);\n            setTimeout(()=>setCopiedPassword(null), 2000) // Clear after 2 seconds\n            ;\n        } catch (error) {\n            console.error(\"Failed to copy password:\", error);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                lineNumber: 137,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n            lineNumber: 136,\n            columnNumber: 7\n        }, this);\n    }\n    if (!user) return null;\n    const nonAdminUsers = allUsers.filter((u)=>u.role !== \"admin\");\n    const roleStats = {\n        detective: nonAdminUsers.filter((u)=>u.role === \"detective\").length,\n        liar: nonAdminUsers.filter((u)=>u.role === \"liar\").length,\n        normal: nonAdminUsers.filter((u)=>u.role === \"normal\").length,\n        unassigned: nonAdminUsers.filter((u)=>!u.role).length\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto py-8 px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow p-6 mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: \"Admin Panel\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: [\n                                            \"Welcome, \",\n                                            user.name\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleLogout,\n                                className: \"px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700\",\n                                children: \"Logout\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                    lineNumber: 159,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-sm font-medium text-yellow-800 mb-2\",\n                            children: \"Password Information:\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-yellow-700 space-y-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Admin user:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-mono bg-yellow-100 px-2 py-1 rounded\",\n                                            children: \"zxcvbnm\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>copyPassword(\"zxcvbnm\", \"admin-default\"),\n                                            className: \"text-yellow-600 hover:text-yellow-800 text-xs\",\n                                            children: copiedPassword === \"admin-default\" ? \"✓ Copied!\" : \"\\uD83D\\uDCCB Copy\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"All other users:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                            lineNumber: 189,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-mono bg-yellow-100 px-2 py-1 rounded\",\n                                            children: \"Random 8-character passwords\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-yellow-600\",\n                                            children: \"(see table below for individual passwords)\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                    lineNumber: 175,\n                    columnNumber: 9\n                }, this),\n                message && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-blue-50 border border-blue-200 rounded-md p-4 mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-blue-800\",\n                        children: message\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                        lineNumber: 199,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                    lineNumber: 198,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleSync,\n                            disabled: syncing,\n                            className: \"bg-blue-600 text-white p-4 rounded-lg hover:bg-blue-700 disabled:opacity-50\",\n                            children: syncing ? \"Syncing...\" : \"Sync Notion\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                            lineNumber: 205,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleClearDB,\n                            className: \"bg-red-600 text-white p-4 rounded-lg hover:bg-red-700\",\n                            children: \"Clear Database\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                            lineNumber: 213,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleAssignRoles,\n                            className: \"bg-green-600 text-white p-4 rounded-lg hover:bg-green-700\",\n                            children: \"Assign Roles\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setShowNotes(!showNotes),\n                            className: \"bg-purple-600 text-white p-4 rounded-lg hover:bg-purple-700\",\n                            children: showNotes ? \"Hide Notes\" : \"View Notes (\".concat(notes.length, \")\")\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                            lineNumber: 227,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                    lineNumber: 204,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow p-6 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-900 mb-4\",\n                            children: \"Role Statistics\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                            lineNumber: 237,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-blue-600\",\n                                            children: roleStats.detective\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                            lineNumber: 240,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"Detective\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                    lineNumber: 239,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-red-600\",\n                                            children: roleStats.liar\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                            lineNumber: 244,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"Liar\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                            lineNumber: 245,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                    lineNumber: 243,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-green-600\",\n                                            children: roleStats.normal\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"Normal\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                    lineNumber: 247,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-gray-600\",\n                                            children: roleStats.unassigned\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"Unassigned\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                    lineNumber: 251,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                            lineNumber: 238,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                    lineNumber: 236,\n                    columnNumber: 9\n                }, this),\n                showNotes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow p-6 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-900 mb-4\",\n                            children: [\n                                \"User Notes (\",\n                                notes.length,\n                                \")\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                            lineNumber: 261,\n                            columnNumber: 13\n                        }, this),\n                        notes.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-500 text-center py-8\",\n                            children: \"No notes submitted yet.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                            lineNumber: 263,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4 max-h-96 overflow-y-auto\",\n                            children: notes.map((note)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border border-gray-200 rounded-lg p-4 bg-gray-50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-start mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-semibold text-gray-900\",\n                                                    children: note.user_name\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                                    lineNumber: 269,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: new Date(note.created_at).toLocaleString()\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                                    lineNumber: 270,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-700 whitespace-pre-wrap\",\n                                            children: note.content\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                            lineNumber: 274,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, note.id, true, {\n                                    fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 19\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                            lineNumber: 265,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                    lineNumber: 260,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-900 mb-4\",\n                            children: [\n                                \"All Users (\",\n                                allUsers.length,\n                                \")\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                            lineNumber: 284,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"overflow-x-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                className: \"min-w-full divide-y divide-gray-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                        className: \"bg-gray-50\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"Name\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                                    lineNumber: 289,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"Role\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                                    lineNumber: 290,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"Email\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                                    lineNumber: 291,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"Password\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                                    lineNumber: 292,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                        className: \"bg-white divide-y divide-gray-200\",\n                                        children: allUsers.map((user)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                                                        children: user.name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                                        lineNumber: 298,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"px-2 py-1 text-xs font-medium rounded-full \".concat(user.role === \"admin\" ? \"bg-purple-100 text-purple-800\" : user.role === \"detective\" ? \"bg-blue-100 text-blue-800\" : user.role === \"liar\" ? \"bg-red-100 text-red-800\" : user.role === \"normal\" ? \"bg-green-100 text-green-800\" : \"bg-gray-100 text-gray-800\"),\n                                                            children: user.role || \"Unassigned\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                                            lineNumber: 300,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                                        lineNumber: 299,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                        children: user.email || \"-\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                                        lineNumber: 310,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-mono bg-gray-100 px-2 py-1 rounded text-xs\",\n                                                                    children: user.displayPassword || \"Unknown\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                                                    lineNumber: 313,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>copyPassword(user.displayPassword || \"\", user.name),\n                                                                    className: \"text-blue-600 hover:text-blue-800 text-xs\",\n                                                                    title: \"Copy password\",\n                                                                    children: copiedPassword === user.name ? \"✓ Copied!\" : \"\\uD83D\\uDCCB Copy\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                                                    lineNumber: 316,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                                            lineNumber: 312,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                                        lineNumber: 311,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, user.id, true, {\n                                                fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                                lineNumber: 297,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                        lineNumber: 295,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                lineNumber: 286,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                            lineNumber: 285,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                    lineNumber: 283,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n            lineNumber: 157,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n        lineNumber: 156,\n        columnNumber: 5\n    }, this);\n}\n_s(AdminPage, \"2X5otNulpI0Hb8RQ587U07LHWcE=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = AdminPage;\nvar _c;\n$RefreshReg$(_c, \"AdminPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9hZG1pbi9wYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUUyQztBQUNBO0FBYTVCLFNBQVNHOztJQUN0QixNQUFNLENBQUNDLE1BQU1DLFFBQVEsR0FBR0wsK0NBQVFBLENBQWM7SUFDOUMsTUFBTSxDQUFDTSxVQUFVQyxZQUFZLEdBQUdQLCtDQUFRQSxDQUFTLEVBQUU7SUFDbkQsTUFBTSxDQUFDUSxTQUFTQyxXQUFXLEdBQUdULCtDQUFRQSxDQUFDO0lBQ3ZDLE1BQU0sQ0FBQ1UsU0FBU0MsV0FBVyxHQUFHWCwrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLENBQUNZLFNBQVNDLFdBQVcsR0FBR2IsK0NBQVFBLENBQUM7SUFDdkMsTUFBTSxDQUFDYyxnQkFBZ0JDLGtCQUFrQixHQUFHZiwrQ0FBUUEsQ0FBZ0I7SUFDcEUsTUFBTSxDQUFDZ0IsT0FBT0MsU0FBUyxHQUFHakIsK0NBQVFBLENBQVEsRUFBRTtJQUM1QyxNQUFNLENBQUNrQixXQUFXQyxhQUFhLEdBQUduQiwrQ0FBUUEsQ0FBQztJQUMzQyxNQUFNb0IsU0FBU2xCLDBEQUFTQTtJQUV4QkQsZ0RBQVNBLENBQUM7UUFDUiwwQ0FBMEM7UUFDMUMsTUFBTW9CLFdBQVdDLGFBQWFDLE9BQU8sQ0FBQztRQUN0QyxJQUFJLENBQUNGLFVBQVU7WUFDYkQsT0FBT0ksSUFBSSxDQUFDO1lBQ1o7UUFDRjtRQUVBLE1BQU1DLFVBQVVDLEtBQUtDLEtBQUssQ0FBQ047UUFDM0IsSUFBSUksUUFBUUcsSUFBSSxLQUFLLFNBQVM7WUFDNUJSLE9BQU9JLElBQUksQ0FBQztZQUNaO1FBQ0Y7UUFFQW5CLFFBQVFvQjtRQUNSSTtRQUNBQztJQUNGLEdBQUc7UUFBQ1Y7S0FBTztJQUVYLE1BQU1TLGFBQWE7UUFDakIsSUFBSTtZQUNGLE1BQU1FLFdBQVcsTUFBTUMsTUFBTTtZQUM3QixNQUFNQyxPQUFPLE1BQU1GLFNBQVNHLElBQUk7WUFFaEMsSUFBSUQsS0FBS0UsT0FBTyxFQUFFO2dCQUNoQjVCLFlBQVkwQixLQUFLRyxLQUFLO1lBQ3hCO1FBQ0YsRUFBRSxPQUFPQyxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQywwQkFBMEJBO1FBQzFDLFNBQVU7WUFDUjVCLFdBQVc7UUFDYjtJQUNGO0lBRUEsTUFBTXFCLGFBQWE7UUFDakIsSUFBSTtZQUNGLE1BQU1DLFdBQVcsTUFBTUMsTUFBTTtZQUM3QixNQUFNQyxPQUFPLE1BQU1GLFNBQVNHLElBQUk7WUFFaEMsSUFBSUQsS0FBS0UsT0FBTyxFQUFFO2dCQUNoQmxCLFNBQVNnQixLQUFLakIsS0FBSztZQUNyQjtRQUNGLEVBQUUsT0FBT3FCLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLDBCQUEwQkE7UUFDMUM7SUFDRjtJQUVBLE1BQU1FLGFBQWE7UUFDakI1QixXQUFXO1FBQ1hFLFdBQVc7UUFFWCxJQUFJO1lBQ0YsTUFBTWtCLFdBQVcsTUFBTUMsTUFBTSxhQUFhO2dCQUFFUSxRQUFRO1lBQU87WUFDM0QsTUFBTVAsT0FBTyxNQUFNRixTQUFTRyxJQUFJO1lBRWhDLElBQUlELEtBQUtFLE9BQU8sRUFBRTtnQkFDaEJ0QixXQUFXb0IsS0FBS3JCLE9BQU87Z0JBQ3ZCaUIsYUFBYSx3QkFBd0I7O2dCQUNyQ0MsYUFBYSxnQkFBZ0I7O1lBQy9CLE9BQU87Z0JBQ0xqQixXQUFXb0IsS0FBS3JCLE9BQU8sSUFBSTtZQUM3QjtRQUNGLEVBQUUsT0FBT3lCLE9BQU87WUFDZHhCLFdBQVc7UUFDYixTQUFVO1lBQ1JGLFdBQVc7UUFDYjtJQUNGO0lBRUEsTUFBTThCLGdCQUFnQjtRQUNwQixJQUFJLENBQUNDLFFBQVEsMEZBQTBGO1lBQ3JHO1FBQ0Y7UUFFQSxJQUFJO1lBQ0YsTUFBTVgsV0FBVyxNQUFNQyxNQUFNLG9CQUFvQjtnQkFBRVEsUUFBUTtZQUFPO1lBQ2xFLE1BQU1QLE9BQU8sTUFBTUYsU0FBU0csSUFBSTtZQUVoQyxJQUFJRCxLQUFLRSxPQUFPLEVBQUU7Z0JBQ2hCdEIsV0FBV29CLEtBQUtyQixPQUFPO2dCQUN2QmlCLGFBQWEsd0JBQXdCOztZQUN2QyxPQUFPO2dCQUNMaEIsV0FBV29CLEtBQUtyQixPQUFPLElBQUk7WUFDN0I7UUFDRixFQUFFLE9BQU95QixPQUFPO1lBQ2R4QixXQUFXO1FBQ2I7SUFDRjtJQUlBLE1BQU04QixlQUFlO1FBQ25CckIsYUFBYXNCLFVBQVUsQ0FBQztRQUN4QnhCLE9BQU9JLElBQUksQ0FBQztJQUNkO0lBRUEsTUFBTXFCLGVBQWUsT0FBT0MsVUFBa0JDO1FBQzVDLElBQUk7WUFDRixNQUFNQyxVQUFVQyxTQUFTLENBQUNDLFNBQVMsQ0FBQ0o7WUFDcEMvQixrQkFBa0JnQztZQUNsQkksV0FBVyxJQUFNcEMsa0JBQWtCLE9BQU8sTUFBTSx3QkFBd0I7O1FBQzFFLEVBQUUsT0FBT3NCLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLDRCQUE0QkE7UUFDNUM7SUFDRjtJQUVBLElBQUk3QixTQUFTO1FBQ1gscUJBQ0UsOERBQUM0QztZQUFJQyxXQUFVO3NCQUNiLDRFQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNEO3dCQUFJQyxXQUFVOzs7Ozs7a0NBQ2YsOERBQUNDO3dCQUFFRCxXQUFVO2tDQUFxQjs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFJMUM7SUFFQSxJQUFJLENBQUNqRCxNQUFNLE9BQU87SUFFbEIsTUFBTW1ELGdCQUFnQmpELFNBQVNrRCxNQUFNLENBQUNDLENBQUFBLElBQUtBLEVBQUU3QixJQUFJLEtBQUs7SUFDdEQsTUFBTThCLFlBQVk7UUFDaEJDLFdBQVdKLGNBQWNDLE1BQU0sQ0FBQ0MsQ0FBQUEsSUFBS0EsRUFBRTdCLElBQUksS0FBSyxhQUFhZ0MsTUFBTTtRQUNuRUMsTUFBTU4sY0FBY0MsTUFBTSxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFN0IsSUFBSSxLQUFLLFFBQVFnQyxNQUFNO1FBQ3pERSxRQUFRUCxjQUFjQyxNQUFNLENBQUNDLENBQUFBLElBQUtBLEVBQUU3QixJQUFJLEtBQUssVUFBVWdDLE1BQU07UUFDN0RHLFlBQVlSLGNBQWNDLE1BQU0sQ0FBQ0MsQ0FBQUEsSUFBSyxDQUFDQSxFQUFFN0IsSUFBSSxFQUFFZ0MsTUFBTTtJQUN2RDtJQUVBLHFCQUNFLDhEQUFDUjtRQUFJQyxXQUFVO2tCQUNiLDRFQUFDRDtZQUFJQyxXQUFVOzs4QkFFYiw4REFBQ0Q7b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7O2tEQUNDLDhEQUFDWTt3Q0FBR1gsV0FBVTtrREFBbUM7Ozs7OztrREFDakQsOERBQUNDO3dDQUFFRCxXQUFVOzs0Q0FBZ0I7NENBQVVqRCxLQUFLNkQsSUFBSTs7Ozs7Ozs7Ozs7OzswQ0FFbEQsOERBQUNDO2dDQUNDQyxTQUFTeEI7Z0NBQ1RVLFdBQVU7MENBQ1g7Ozs7Ozs7Ozs7Ozs7Ozs7OzhCQU9MLDhEQUFDRDtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNlOzRCQUFHZixXQUFVO3NDQUEyQzs7Ozs7O3NDQUN6RCw4REFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDRDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNnQjtzREFBSzs7Ozs7O3NEQUNOLDhEQUFDQTs0Q0FBS2hCLFdBQVU7c0RBQTRDOzs7Ozs7c0RBQzVELDhEQUFDYTs0Q0FDQ0MsU0FBUyxJQUFNdEIsYUFBYSxXQUFXOzRDQUN2Q1EsV0FBVTtzREFFVHZDLG1CQUFtQixrQkFBa0IsY0FBYzs7Ozs7Ozs7Ozs7OzhDQUd4RCw4REFBQ3NDO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ2dCO3NEQUFLOzs7Ozs7c0RBQ04sOERBQUNBOzRDQUFLaEIsV0FBVTtzREFBNEM7Ozs7OztzREFDNUQsOERBQUNnQjs0Q0FBS2hCLFdBQVU7c0RBQTBCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Z0JBTS9DekMseUJBQ0MsOERBQUN3QztvQkFBSUMsV0FBVTs4QkFDYiw0RUFBQ0M7d0JBQUVELFdBQVU7a0NBQWlCekM7Ozs7Ozs7Ozs7OzhCQUtsQyw4REFBQ3dDO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ2E7NEJBQ0NDLFNBQVM1Qjs0QkFDVCtCLFVBQVU1RDs0QkFDVjJDLFdBQVU7c0NBRVQzQyxVQUFVLGVBQWU7Ozs7OztzQ0FHNUIsOERBQUN3RDs0QkFDQ0MsU0FBUzFCOzRCQUNUWSxXQUFVO3NDQUNYOzs7Ozs7c0NBSUQsOERBQUNhOzRCQUNDQyxTQUFTSTs0QkFDVGxCLFdBQVU7c0NBQ1g7Ozs7OztzQ0FJRCw4REFBQ2E7NEJBQ0NDLFNBQVMsSUFBTWhELGFBQWEsQ0FBQ0Q7NEJBQzdCbUMsV0FBVTtzQ0FFVG5DLFlBQVksZUFBZSxlQUE0QixPQUFiRixNQUFNNEMsTUFBTSxFQUFDOzs7Ozs7Ozs7Ozs7OEJBSzVELDhEQUFDUjtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNtQjs0QkFBR25CLFdBQVU7c0NBQTJDOzs7Ozs7c0NBQ3pELDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ0Q7NENBQUlDLFdBQVU7c0RBQW9DSyxVQUFVQyxTQUFTOzs7Ozs7c0RBQ3RFLDhEQUFDUDs0Q0FBSUMsV0FBVTtzREFBd0I7Ozs7Ozs7Ozs7Ozs4Q0FFekMsOERBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ0Q7NENBQUlDLFdBQVU7c0RBQW1DSyxVQUFVRyxJQUFJOzs7Ozs7c0RBQ2hFLDhEQUFDVDs0Q0FBSUMsV0FBVTtzREFBd0I7Ozs7Ozs7Ozs7Ozs4Q0FFekMsOERBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ0Q7NENBQUlDLFdBQVU7c0RBQXFDSyxVQUFVSSxNQUFNOzs7Ozs7c0RBQ3BFLDhEQUFDVjs0Q0FBSUMsV0FBVTtzREFBd0I7Ozs7Ozs7Ozs7Ozs4Q0FFekMsOERBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ0Q7NENBQUlDLFdBQVU7c0RBQW9DSyxVQUFVSyxVQUFVOzs7Ozs7c0RBQ3ZFLDhEQUFDWDs0Q0FBSUMsV0FBVTtzREFBd0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztnQkFNNUNuQywyQkFDQyw4REFBQ2tDO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ21COzRCQUFHbkIsV0FBVTs7Z0NBQTJDO2dDQUFhckMsTUFBTTRDLE1BQU07Z0NBQUM7Ozs7Ozs7d0JBQ2xGNUMsTUFBTTRDLE1BQU0sS0FBSyxrQkFDaEIsOERBQUNOOzRCQUFFRCxXQUFVO3NDQUFpQzs7Ozs7aURBRTlDLDhEQUFDRDs0QkFBSUMsV0FBVTtzQ0FDWnJDLE1BQU15RCxHQUFHLENBQUMsQ0FBQ0MscUJBQ1YsOERBQUN0QjtvQ0FBa0JDLFdBQVU7O3NEQUMzQiw4REFBQ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDZ0I7b0RBQUtoQixXQUFVOzhEQUErQnFCLEtBQUtDLFNBQVM7Ozs7Ozs4REFDN0QsOERBQUNOO29EQUFLaEIsV0FBVTs4REFDYixJQUFJdUIsS0FBS0YsS0FBS0csVUFBVSxFQUFFQyxjQUFjOzs7Ozs7Ozs7Ozs7c0RBRzdDLDhEQUFDeEI7NENBQUVELFdBQVU7c0RBQXFDcUIsS0FBS0ssT0FBTzs7Ozs7OzttQ0FQdERMLEtBQUtNLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBZ0IzQiw4REFBQzVCO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ21COzRCQUFHbkIsV0FBVTs7Z0NBQTJDO2dDQUFZL0MsU0FBU3NELE1BQU07Z0NBQUM7Ozs7Ozs7c0NBQ3JGLDhEQUFDUjs0QkFBSUMsV0FBVTtzQ0FDYiw0RUFBQzRCO2dDQUFNNUIsV0FBVTs7a0RBQ2YsOERBQUM2Qjt3Q0FBTTdCLFdBQVU7a0RBQ2YsNEVBQUM4Qjs7OERBQ0MsOERBQUNDO29EQUFHL0IsV0FBVTs4REFBaUY7Ozs7Ozs4REFDL0YsOERBQUMrQjtvREFBRy9CLFdBQVU7OERBQWlGOzs7Ozs7OERBQy9GLDhEQUFDK0I7b0RBQUcvQixXQUFVOzhEQUFpRjs7Ozs7OzhEQUMvRiw4REFBQytCO29EQUFHL0IsV0FBVTs4REFBaUY7Ozs7Ozs7Ozs7Ozs7Ozs7O2tEQUduRyw4REFBQ2dDO3dDQUFNaEMsV0FBVTtrREFDZC9DLFNBQVNtRSxHQUFHLENBQUMsQ0FBQ3JFLHFCQUNiLDhEQUFDK0U7O2tFQUNDLDhEQUFDRzt3REFBR2pDLFdBQVU7a0VBQWlFakQsS0FBSzZELElBQUk7Ozs7OztrRUFDeEYsOERBQUNxQjt3REFBR2pDLFdBQVU7a0VBQ1osNEVBQUNnQjs0REFBS2hCLFdBQVcsOENBTWhCLE9BTENqRCxLQUFLd0IsSUFBSSxLQUFLLFVBQVUsa0NBQ3hCeEIsS0FBS3dCLElBQUksS0FBSyxjQUFjLDhCQUM1QnhCLEtBQUt3QixJQUFJLEtBQUssU0FBUyw0QkFDdkJ4QixLQUFLd0IsSUFBSSxLQUFLLFdBQVcsZ0NBQ3pCO3NFQUVDeEIsS0FBS3dCLElBQUksSUFBSTs7Ozs7Ozs7Ozs7a0VBR2xCLDhEQUFDMEQ7d0RBQUdqQyxXQUFVO2tFQUFxRGpELEtBQUttRixLQUFLLElBQUk7Ozs7OztrRUFDakYsOERBQUNEO3dEQUFHakMsV0FBVTtrRUFDWiw0RUFBQ0Q7NERBQUlDLFdBQVU7OzhFQUNiLDhEQUFDZ0I7b0VBQUtoQixXQUFVOzhFQUNiakQsS0FBS29GLGVBQWUsSUFBSTs7Ozs7OzhFQUUzQiw4REFBQ3RCO29FQUNDQyxTQUFTLElBQU10QixhQUFhekMsS0FBS29GLGVBQWUsSUFBSSxJQUFJcEYsS0FBSzZELElBQUk7b0VBQ2pFWixXQUFVO29FQUNWb0MsT0FBTTs4RUFFTDNFLG1CQUFtQlYsS0FBSzZELElBQUksR0FBRyxjQUFjOzs7Ozs7Ozs7Ozs7Ozs7Ozs7K0NBeEI3QzdELEtBQUs0RSxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQXFDbEM7R0E3VHdCN0U7O1FBU1BELHNEQUFTQTs7O0tBVEZDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL2FwcC9hZG1pbi9wYWdlLnRzeD82YWE1Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgeyB1c2VSb3V0ZXIgfSBmcm9tICduZXh0L25hdmlnYXRpb24nXG5cbmludGVyZmFjZSBVc2VyIHtcbiAgaWQ6IG51bWJlclxuICBuYW1lOiBzdHJpbmdcbiAgZW1haWw6IHN0cmluZyB8IG51bGxcbiAgcGhvbmU6IHN0cmluZyB8IG51bGxcbiAgYWJvdXQ6IHN0cmluZyB8IG51bGxcbiAgc2VsZmllOiBzdHJpbmcgfCBudWxsXG4gIHJvbGU6ICdkZXRlY3RpdmUnIHwgJ2xpYXInIHwgJ25vcm1hbCcgfCAnYWRtaW4nXG4gIGRpc3BsYXlQYXNzd29yZD86IHN0cmluZ1xufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBBZG1pblBhZ2UoKSB7XG4gIGNvbnN0IFt1c2VyLCBzZXRVc2VyXSA9IHVzZVN0YXRlPFVzZXIgfCBudWxsPihudWxsKVxuICBjb25zdCBbYWxsVXNlcnMsIHNldEFsbFVzZXJzXSA9IHVzZVN0YXRlPFVzZXJbXT4oW10pXG4gIGNvbnN0IFtsb2FkaW5nLCBzZXRMb2FkaW5nXSA9IHVzZVN0YXRlKHRydWUpXG4gIGNvbnN0IFtzeW5jaW5nLCBzZXRTeW5jaW5nXSA9IHVzZVN0YXRlKGZhbHNlKVxuICBjb25zdCBbbWVzc2FnZSwgc2V0TWVzc2FnZV0gPSB1c2VTdGF0ZSgnJylcbiAgY29uc3QgW2NvcGllZFBhc3N3b3JkLCBzZXRDb3BpZWRQYXNzd29yZF0gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihudWxsKVxuICBjb25zdCBbbm90ZXMsIHNldE5vdGVzXSA9IHVzZVN0YXRlPGFueVtdPihbXSlcbiAgY29uc3QgW3Nob3dOb3Rlcywgc2V0U2hvd05vdGVzXSA9IHVzZVN0YXRlKGZhbHNlKVxuICBjb25zdCByb3V0ZXIgPSB1c2VSb3V0ZXIoKVxuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgLy8gQ2hlY2sgaWYgdXNlciBpcyBsb2dnZWQgaW4gYW5kIGlzIGFkbWluXG4gICAgY29uc3QgdXNlckRhdGEgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgndXNlcicpXG4gICAgaWYgKCF1c2VyRGF0YSkge1xuICAgICAgcm91dGVyLnB1c2goJy8nKVxuICAgICAgcmV0dXJuXG4gICAgfVxuXG4gICAgY29uc3QgdXNlck9iaiA9IEpTT04ucGFyc2UodXNlckRhdGEpXG4gICAgaWYgKHVzZXJPYmoucm9sZSAhPT0gJ2FkbWluJykge1xuICAgICAgcm91dGVyLnB1c2goJy9kYXNoYm9hcmQnKVxuICAgICAgcmV0dXJuXG4gICAgfVxuXG4gICAgc2V0VXNlcih1c2VyT2JqKVxuICAgIGZldGNoVXNlcnMoKVxuICAgIGZldGNoTm90ZXMoKVxuICB9LCBbcm91dGVyXSlcblxuICBjb25zdCBmZXRjaFVzZXJzID0gYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL2FkbWluL3VzZXJzJylcbiAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKClcblxuICAgICAgaWYgKGRhdGEuc3VjY2Vzcykge1xuICAgICAgICBzZXRBbGxVc2VycyhkYXRhLnVzZXJzKVxuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gZmV0Y2ggdXNlcnM6JywgZXJyb3IpXG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpXG4gICAgfVxuICB9XG5cbiAgY29uc3QgZmV0Y2hOb3RlcyA9IGFzeW5jICgpID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnL2FwaS9ub3RlcycpXG4gICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpXG5cbiAgICAgIGlmIChkYXRhLnN1Y2Nlc3MpIHtcbiAgICAgICAgc2V0Tm90ZXMoZGF0YS5ub3RlcylcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIGZldGNoIG5vdGVzOicsIGVycm9yKVxuICAgIH1cbiAgfVxuXG4gIGNvbnN0IGhhbmRsZVN5bmMgPSBhc3luYyAoKSA9PiB7XG4gICAgc2V0U3luY2luZyh0cnVlKVxuICAgIHNldE1lc3NhZ2UoJycpXG4gICAgXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGkvc3luYycsIHsgbWV0aG9kOiAnUE9TVCcgfSlcbiAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKClcbiAgICAgIFxuICAgICAgaWYgKGRhdGEuc3VjY2Vzcykge1xuICAgICAgICBzZXRNZXNzYWdlKGRhdGEubWVzc2FnZSlcbiAgICAgICAgZmV0Y2hVc2VycygpIC8vIFJlZnJlc2ggdGhlIHVzZXIgbGlzdFxuICAgICAgICBmZXRjaE5vdGVzKCkgLy8gUmVmcmVzaCBub3Rlc1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgc2V0TWVzc2FnZShkYXRhLm1lc3NhZ2UgfHwgJ1N5bmMgZmFpbGVkJylcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgc2V0TWVzc2FnZSgnTmV0d29yayBlcnJvciBkdXJpbmcgc3luYycpXG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldFN5bmNpbmcoZmFsc2UpXG4gICAgfVxuICB9XG5cbiAgY29uc3QgaGFuZGxlQ2xlYXJEQiA9IGFzeW5jICgpID0+IHtcbiAgICBpZiAoIWNvbmZpcm0oJ0FyZSB5b3Ugc3VyZSB5b3Ugd2FudCB0byBjbGVhciB0aGUgZGF0YWJhc2U/IFRoaXMgd2lsbCByZW1vdmUgYWxsIHVzZXJzIGV4Y2VwdCBhZG1pbi4nKSkge1xuICAgICAgcmV0dXJuXG4gICAgfVxuICAgIFxuICAgIHRyeSB7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL2FkbWluL2NsZWFyJywgeyBtZXRob2Q6ICdQT1NUJyB9KVxuICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKVxuICAgICAgXG4gICAgICBpZiAoZGF0YS5zdWNjZXNzKSB7XG4gICAgICAgIHNldE1lc3NhZ2UoZGF0YS5tZXNzYWdlKVxuICAgICAgICBmZXRjaFVzZXJzKCkgLy8gUmVmcmVzaCB0aGUgdXNlciBsaXN0XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBzZXRNZXNzYWdlKGRhdGEubWVzc2FnZSB8fCAnQ2xlYXIgZmFpbGVkJylcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgc2V0TWVzc2FnZSgnTmV0d29yayBlcnJvciBkdXJpbmcgY2xlYXInKVxuICAgIH1cbiAgfVxuXG5cblxuICBjb25zdCBoYW5kbGVMb2dvdXQgPSAoKSA9PiB7XG4gICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oJ3VzZXInKVxuICAgIHJvdXRlci5wdXNoKCcvJylcbiAgfVxuXG4gIGNvbnN0IGNvcHlQYXNzd29yZCA9IGFzeW5jIChwYXNzd29yZDogc3RyaW5nLCB1c2VyTmFtZTogc3RyaW5nKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGF3YWl0IG5hdmlnYXRvci5jbGlwYm9hcmQud3JpdGVUZXh0KHBhc3N3b3JkKVxuICAgICAgc2V0Q29waWVkUGFzc3dvcmQodXNlck5hbWUpXG4gICAgICBzZXRUaW1lb3V0KCgpID0+IHNldENvcGllZFBhc3N3b3JkKG51bGwpLCAyMDAwKSAvLyBDbGVhciBhZnRlciAyIHNlY29uZHNcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIGNvcHkgcGFzc3dvcmQ6JywgZXJyb3IpXG4gICAgfVxuICB9XG5cbiAgaWYgKGxvYWRpbmcpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYW5pbWF0ZS1zcGluIHJvdW5kZWQtZnVsbCBoLTEyIHctMTIgYm9yZGVyLWItMiBib3JkZXItaW5kaWdvLTYwMCBteC1hdXRvXCI+PC9kaXY+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwibXQtNCB0ZXh0LWdyYXktNjAwXCI+TG9hZGluZy4uLjwvcD5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICApXG4gIH1cblxuICBpZiAoIXVzZXIpIHJldHVybiBudWxsXG5cbiAgY29uc3Qgbm9uQWRtaW5Vc2VycyA9IGFsbFVzZXJzLmZpbHRlcih1ID0+IHUucm9sZSAhPT0gJ2FkbWluJylcbiAgY29uc3Qgcm9sZVN0YXRzID0ge1xuICAgIGRldGVjdGl2ZTogbm9uQWRtaW5Vc2Vycy5maWx0ZXIodSA9PiB1LnJvbGUgPT09ICdkZXRlY3RpdmUnKS5sZW5ndGgsXG4gICAgbGlhcjogbm9uQWRtaW5Vc2Vycy5maWx0ZXIodSA9PiB1LnJvbGUgPT09ICdsaWFyJykubGVuZ3RoLFxuICAgIG5vcm1hbDogbm9uQWRtaW5Vc2Vycy5maWx0ZXIodSA9PiB1LnJvbGUgPT09ICdub3JtYWwnKS5sZW5ndGgsXG4gICAgdW5hc3NpZ25lZDogbm9uQWRtaW5Vc2Vycy5maWx0ZXIodSA9PiAhdS5yb2xlKS5sZW5ndGhcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gYmctZ3JheS01MFwiPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy02eGwgbXgtYXV0byBweS04IHB4LTRcIj5cbiAgICAgICAgey8qIEhlYWRlciAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLWxnIHNoYWRvdyBwLTYgbWItOFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDBcIj5BZG1pbiBQYW5lbDwvaDE+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDBcIj5XZWxjb21lLCB7dXNlci5uYW1lfTwvcD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVMb2dvdXR9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTQgcHktMiBiZy1yZWQtNjAwIHRleHQtd2hpdGUgcm91bmRlZC1tZCBob3ZlcjpiZy1yZWQtNzAwXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgTG9nb3V0XG4gICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIFBhc3N3b3JkIEluZm9ybWF0aW9uICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXllbGxvdy01MCBib3JkZXIgYm9yZGVyLXllbGxvdy0yMDAgcm91bmRlZC1tZCBwLTQgbWItNlwiPlxuICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQteWVsbG93LTgwMCBtYi0yXCI+UGFzc3dvcmQgSW5mb3JtYXRpb246PC9oMz5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC15ZWxsb3ctNzAwIHNwYWNlLXktMVwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgICAgPHNwYW4+QWRtaW4gdXNlcjo8L3NwYW4+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZvbnQtbW9ubyBiZy15ZWxsb3ctMTAwIHB4LTIgcHktMSByb3VuZGVkXCI+enhjdmJubTwvc3Bhbj5cbiAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGNvcHlQYXNzd29yZCgnenhjdmJubScsICdhZG1pbi1kZWZhdWx0Jyl9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC15ZWxsb3ctNjAwIGhvdmVyOnRleHQteWVsbG93LTgwMCB0ZXh0LXhzXCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIHtjb3BpZWRQYXNzd29yZCA9PT0gJ2FkbWluLWRlZmF1bHQnID8gJ+KckyBDb3BpZWQhJyA6ICfwn5OLIENvcHknfVxuICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgICAgPHNwYW4+QWxsIG90aGVyIHVzZXJzOjwvc3Bhbj5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1tb25vIGJnLXllbGxvdy0xMDAgcHgtMiBweS0xIHJvdW5kZWRcIj5SYW5kb20gOC1jaGFyYWN0ZXIgcGFzc3dvcmRzPC9zcGFuPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQteWVsbG93LTYwMFwiPihzZWUgdGFibGUgYmVsb3cgZm9yIGluZGl2aWR1YWwgcGFzc3dvcmRzKTwvc3Bhbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogTWVzc2FnZSAqL31cbiAgICAgICAge21lc3NhZ2UgJiYgKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctYmx1ZS01MCBib3JkZXIgYm9yZGVyLWJsdWUtMjAwIHJvdW5kZWQtbWQgcC00IG1iLTZcIj5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtYmx1ZS04MDBcIj57bWVzc2FnZX08L3A+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICl9XG5cbiAgICAgICAgey8qIEFjdGlvbnMgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtNCBnYXAtNiBtYi04XCI+XG4gICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgb25DbGljaz17aGFuZGxlU3luY31cbiAgICAgICAgICAgIGRpc2FibGVkPXtzeW5jaW5nfVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctYmx1ZS02MDAgdGV4dC13aGl0ZSBwLTQgcm91bmRlZC1sZyBob3ZlcjpiZy1ibHVlLTcwMCBkaXNhYmxlZDpvcGFjaXR5LTUwXCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICB7c3luY2luZyA/ICdTeW5jaW5nLi4uJyA6ICdTeW5jIE5vdGlvbid9XG4gICAgICAgICAgPC9idXR0b24+XG5cbiAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVDbGVhckRCfVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctcmVkLTYwMCB0ZXh0LXdoaXRlIHAtNCByb3VuZGVkLWxnIGhvdmVyOmJnLXJlZC03MDBcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIENsZWFyIERhdGFiYXNlXG4gICAgICAgICAgPC9idXR0b24+XG5cbiAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVBc3NpZ25Sb2xlc31cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWdyZWVuLTYwMCB0ZXh0LXdoaXRlIHAtNCByb3VuZGVkLWxnIGhvdmVyOmJnLWdyZWVuLTcwMFwiXG4gICAgICAgICAgPlxuICAgICAgICAgICAgQXNzaWduIFJvbGVzXG4gICAgICAgICAgPC9idXR0b24+XG5cbiAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTaG93Tm90ZXMoIXNob3dOb3Rlcyl9XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1wdXJwbGUtNjAwIHRleHQtd2hpdGUgcC00IHJvdW5kZWQtbGcgaG92ZXI6YmctcHVycGxlLTcwMFwiXG4gICAgICAgICAgPlxuICAgICAgICAgICAge3Nob3dOb3RlcyA/ICdIaWRlIE5vdGVzJyA6IGBWaWV3IE5vdGVzICgke25vdGVzLmxlbmd0aH0pYH1cbiAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIFN0YXRzICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQtbGcgc2hhZG93IHAtNiBtYi04XCI+XG4gICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktOTAwIG1iLTRcIj5Sb2xlIFN0YXRpc3RpY3M8L2gyPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMiBtZDpncmlkLWNvbHMtNCBnYXAtNFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWJsdWUtNjAwXCI+e3JvbGVTdGF0cy5kZXRlY3RpdmV9PC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+RGV0ZWN0aXZlPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1yZWQtNjAwXCI+e3JvbGVTdGF0cy5saWFyfTwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPkxpYXI8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWdyZWVuLTYwMFwiPntyb2xlU3RhdHMubm9ybWFsfTwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPk5vcm1hbDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtZ3JheS02MDBcIj57cm9sZVN0YXRzLnVuYXNzaWduZWR9PC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+VW5hc3NpZ25lZDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBOb3RlcyBTZWN0aW9uICovfVxuICAgICAgICB7c2hvd05vdGVzICYmIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQtbGcgc2hhZG93IHAtNiBtYi04XCI+XG4gICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDAgbWItNFwiPlVzZXIgTm90ZXMgKHtub3Rlcy5sZW5ndGh9KTwvaDI+XG4gICAgICAgICAgICB7bm90ZXMubGVuZ3RoID09PSAwID8gKFxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNTAwIHRleHQtY2VudGVyIHB5LThcIj5ObyBub3RlcyBzdWJtaXR0ZWQgeWV0LjwvcD5cbiAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00IG1heC1oLTk2IG92ZXJmbG93LXktYXV0b1wiPlxuICAgICAgICAgICAgICAgIHtub3Rlcy5tYXAoKG5vdGUpID0+IChcbiAgICAgICAgICAgICAgICAgIDxkaXYga2V5PXtub3RlLmlkfSBjbGFzc05hbWU9XCJib3JkZXIgYm9yZGVyLWdyYXktMjAwIHJvdW5kZWQtbGcgcC00IGJnLWdyYXktNTBcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1zdGFydCBtYi0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktOTAwXCI+e25vdGUudXNlcl9uYW1lfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS01MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtuZXcgRGF0ZShub3RlLmNyZWF0ZWRfYXQpLnRvTG9jYWxlU3RyaW5nKCl9XG4gICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTcwMCB3aGl0ZXNwYWNlLXByZS13cmFwXCI+e25vdGUuY29udGVudH08L3A+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApfVxuXG4gICAgICAgIHsvKiBVc2VycyBMaXN0ICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQtbGcgc2hhZG93IHAtNlwiPlxuICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMCBtYi00XCI+QWxsIFVzZXJzICh7YWxsVXNlcnMubGVuZ3RofSk8L2gyPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwib3ZlcmZsb3cteC1hdXRvXCI+XG4gICAgICAgICAgICA8dGFibGUgY2xhc3NOYW1lPVwibWluLXctZnVsbCBkaXZpZGUteSBkaXZpZGUtZ3JheS0yMDBcIj5cbiAgICAgICAgICAgICAgPHRoZWFkIGNsYXNzTmFtZT1cImJnLWdyYXktNTBcIj5cbiAgICAgICAgICAgICAgICA8dHI+XG4gICAgICAgICAgICAgICAgICA8dGggY2xhc3NOYW1lPVwicHgtNiBweS0zIHRleHQtbGVmdCB0ZXh0LXhzIGZvbnQtbWVkaXVtIHRleHQtZ3JheS01MDAgdXBwZXJjYXNlIHRyYWNraW5nLXdpZGVyXCI+TmFtZTwvdGg+XG4gICAgICAgICAgICAgICAgICA8dGggY2xhc3NOYW1lPVwicHgtNiBweS0zIHRleHQtbGVmdCB0ZXh0LXhzIGZvbnQtbWVkaXVtIHRleHQtZ3JheS01MDAgdXBwZXJjYXNlIHRyYWNraW5nLXdpZGVyXCI+Um9sZTwvdGg+XG4gICAgICAgICAgICAgICAgICA8dGggY2xhc3NOYW1lPVwicHgtNiBweS0zIHRleHQtbGVmdCB0ZXh0LXhzIGZvbnQtbWVkaXVtIHRleHQtZ3JheS01MDAgdXBwZXJjYXNlIHRyYWNraW5nLXdpZGVyXCI+RW1haWw8L3RoPlxuICAgICAgICAgICAgICAgICAgPHRoIGNsYXNzTmFtZT1cInB4LTYgcHktMyB0ZXh0LWxlZnQgdGV4dC14cyBmb250LW1lZGl1bSB0ZXh0LWdyYXktNTAwIHVwcGVyY2FzZSB0cmFja2luZy13aWRlclwiPlBhc3N3b3JkPC90aD5cbiAgICAgICAgICAgICAgICA8L3RyPlxuICAgICAgICAgICAgICA8L3RoZWFkPlxuICAgICAgICAgICAgICA8dGJvZHkgY2xhc3NOYW1lPVwiYmctd2hpdGUgZGl2aWRlLXkgZGl2aWRlLWdyYXktMjAwXCI+XG4gICAgICAgICAgICAgICAge2FsbFVzZXJzLm1hcCgodXNlcikgPT4gKFxuICAgICAgICAgICAgICAgICAgPHRyIGtleT17dXNlci5pZH0+XG4gICAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweC02IHB5LTQgd2hpdGVzcGFjZS1ub3dyYXAgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwXCI+e3VzZXIubmFtZX08L3RkPlxuICAgICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHgtNiBweS00IHdoaXRlc3BhY2Utbm93cmFwIHRleHQtc20gdGV4dC1ncmF5LTUwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT17YHB4LTIgcHktMSB0ZXh0LXhzIGZvbnQtbWVkaXVtIHJvdW5kZWQtZnVsbCAke1xuICAgICAgICAgICAgICAgICAgICAgICAgdXNlci5yb2xlID09PSAnYWRtaW4nID8gJ2JnLXB1cnBsZS0xMDAgdGV4dC1wdXJwbGUtODAwJyA6XG4gICAgICAgICAgICAgICAgICAgICAgICB1c2VyLnJvbGUgPT09ICdkZXRlY3RpdmUnID8gJ2JnLWJsdWUtMTAwIHRleHQtYmx1ZS04MDAnIDpcbiAgICAgICAgICAgICAgICAgICAgICAgIHVzZXIucm9sZSA9PT0gJ2xpYXInID8gJ2JnLXJlZC0xMDAgdGV4dC1yZWQtODAwJyA6XG4gICAgICAgICAgICAgICAgICAgICAgICB1c2VyLnJvbGUgPT09ICdub3JtYWwnID8gJ2JnLWdyZWVuLTEwMCB0ZXh0LWdyZWVuLTgwMCcgOlxuICAgICAgICAgICAgICAgICAgICAgICAgJ2JnLWdyYXktMTAwIHRleHQtZ3JheS04MDAnXG4gICAgICAgICAgICAgICAgICAgICAgfWB9PlxuICAgICAgICAgICAgICAgICAgICAgICAge3VzZXIucm9sZSB8fCAnVW5hc3NpZ25lZCd9XG4gICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHgtNiBweS00IHdoaXRlc3BhY2Utbm93cmFwIHRleHQtc20gdGV4dC1ncmF5LTUwMFwiPnt1c2VyLmVtYWlsIHx8ICctJ308L3RkPlxuICAgICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHgtNiBweS00IHdoaXRlc3BhY2Utbm93cmFwIHRleHQtc20gdGV4dC1ncmF5LTUwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmb250LW1vbm8gYmctZ3JheS0xMDAgcHgtMiBweS0xIHJvdW5kZWQgdGV4dC14c1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7dXNlci5kaXNwbGF5UGFzc3dvcmQgfHwgJ1Vua25vd24nfVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBjb3B5UGFzc3dvcmQodXNlci5kaXNwbGF5UGFzc3dvcmQgfHwgJycsIHVzZXIubmFtZSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtYmx1ZS02MDAgaG92ZXI6dGV4dC1ibHVlLTgwMCB0ZXh0LXhzXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU9XCJDb3B5IHBhc3N3b3JkXCJcbiAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge2NvcGllZFBhc3N3b3JkID09PSB1c2VyLm5hbWUgPyAn4pyTIENvcGllZCEnIDogJ/Cfk4sgQ29weSd9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC90ZD5cbiAgICAgICAgICAgICAgICAgIDwvdHI+XG4gICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgIDwvdGJvZHk+XG4gICAgICAgICAgICA8L3RhYmxlPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApXG59XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJ1c2VSb3V0ZXIiLCJBZG1pblBhZ2UiLCJ1c2VyIiwic2V0VXNlciIsImFsbFVzZXJzIiwic2V0QWxsVXNlcnMiLCJsb2FkaW5nIiwic2V0TG9hZGluZyIsInN5bmNpbmciLCJzZXRTeW5jaW5nIiwibWVzc2FnZSIsInNldE1lc3NhZ2UiLCJjb3BpZWRQYXNzd29yZCIsInNldENvcGllZFBhc3N3b3JkIiwibm90ZXMiLCJzZXROb3RlcyIsInNob3dOb3RlcyIsInNldFNob3dOb3RlcyIsInJvdXRlciIsInVzZXJEYXRhIiwibG9jYWxTdG9yYWdlIiwiZ2V0SXRlbSIsInB1c2giLCJ1c2VyT2JqIiwiSlNPTiIsInBhcnNlIiwicm9sZSIsImZldGNoVXNlcnMiLCJmZXRjaE5vdGVzIiwicmVzcG9uc2UiLCJmZXRjaCIsImRhdGEiLCJqc29uIiwic3VjY2VzcyIsInVzZXJzIiwiZXJyb3IiLCJjb25zb2xlIiwiaGFuZGxlU3luYyIsIm1ldGhvZCIsImhhbmRsZUNsZWFyREIiLCJjb25maXJtIiwiaGFuZGxlTG9nb3V0IiwicmVtb3ZlSXRlbSIsImNvcHlQYXNzd29yZCIsInBhc3N3b3JkIiwidXNlck5hbWUiLCJuYXZpZ2F0b3IiLCJjbGlwYm9hcmQiLCJ3cml0ZVRleHQiLCJzZXRUaW1lb3V0IiwiZGl2IiwiY2xhc3NOYW1lIiwicCIsIm5vbkFkbWluVXNlcnMiLCJmaWx0ZXIiLCJ1Iiwicm9sZVN0YXRzIiwiZGV0ZWN0aXZlIiwibGVuZ3RoIiwibGlhciIsIm5vcm1hbCIsInVuYXNzaWduZWQiLCJoMSIsIm5hbWUiLCJidXR0b24iLCJvbkNsaWNrIiwiaDMiLCJzcGFuIiwiZGlzYWJsZWQiLCJoYW5kbGVBc3NpZ25Sb2xlcyIsImgyIiwibWFwIiwibm90ZSIsInVzZXJfbmFtZSIsIkRhdGUiLCJjcmVhdGVkX2F0IiwidG9Mb2NhbGVTdHJpbmciLCJjb250ZW50IiwiaWQiLCJ0YWJsZSIsInRoZWFkIiwidHIiLCJ0aCIsInRib2R5IiwidGQiLCJlbWFpbCIsImRpc3BsYXlQYXNzd29yZCIsInRpdGxlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/admin/page.tsx\n"));

/***/ })

});