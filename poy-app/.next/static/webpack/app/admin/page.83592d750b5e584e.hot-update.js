"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./app/admin/page.tsx":
/*!****************************!*\
  !*** ./app/admin/page.tsx ***!
  \****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AdminPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction AdminPage() {\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [allUsers, setAllUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [syncing, setSyncing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [copiedPassword, setCopiedPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [notes, setNotes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showNotes, setShowNotes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Check if user is logged in and is admin\n        const userData = localStorage.getItem(\"user\");\n        if (!userData) {\n            router.push(\"/\");\n            return;\n        }\n        const userObj = JSON.parse(userData);\n        if (userObj.role !== \"admin\") {\n            router.push(\"/dashboard\");\n            return;\n        }\n        setUser(userObj);\n        fetchUsers();\n        fetchNotes();\n    }, [\n        router\n    ]);\n    const fetchUsers = async ()=>{\n        try {\n            const response = await fetch(\"/api/admin/users\");\n            const data = await response.json();\n            if (data.success) {\n                setAllUsers(data.users);\n            }\n        } catch (error) {\n            console.error(\"Failed to fetch users:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const fetchNotes = async ()=>{\n        try {\n            const response = await fetch(\"/api/notes\");\n            const data = await response.json();\n            if (data.success) {\n                setNotes(data.notes);\n            }\n        } catch (error) {\n            console.error(\"Failed to fetch notes:\", error);\n        }\n    };\n    const handleSync = async ()=>{\n        setSyncing(true);\n        setMessage(\"\");\n        try {\n            const response = await fetch(\"/api/sync\", {\n                method: \"POST\"\n            });\n            const data = await response.json();\n            if (data.success) {\n                setMessage(data.message);\n                fetchUsers() // Refresh the user list\n                ;\n                fetchNotes() // Refresh notes\n                ;\n            } else {\n                setMessage(data.message || \"Sync failed\");\n            }\n        } catch (error) {\n            setMessage(\"Network error during sync\");\n        } finally{\n            setSyncing(false);\n        }\n    };\n    const handleClearDB = async ()=>{\n        if (!confirm(\"Are you sure you want to clear the database? This will remove all users except admin.\")) {\n            return;\n        }\n        try {\n            const response = await fetch(\"/api/admin/clear\", {\n                method: \"POST\"\n            });\n            const data = await response.json();\n            if (data.success) {\n                setMessage(data.message);\n                fetchUsers() // Refresh the user list\n                ;\n            } else {\n                setMessage(data.message || \"Clear failed\");\n            }\n        } catch (error) {\n            setMessage(\"Network error during clear\");\n        }\n    };\n    const handleLogout = ()=>{\n        localStorage.removeItem(\"user\");\n        router.push(\"/\");\n    };\n    const copyPassword = async (password, userName)=>{\n        try {\n            await navigator.clipboard.writeText(password);\n            setCopiedPassword(userName);\n            setTimeout(()=>setCopiedPassword(null), 2000) // Clear after 2 seconds\n            ;\n        } catch (error) {\n            console.error(\"Failed to copy password:\", error);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                lineNumber: 137,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n            lineNumber: 136,\n            columnNumber: 7\n        }, this);\n    }\n    if (!user) return null;\n    const nonAdminUsers = allUsers.filter((u)=>u.role !== \"admin\");\n    const roleStats = {\n        detective: nonAdminUsers.filter((u)=>u.role === \"detective\").length,\n        liar: nonAdminUsers.filter((u)=>u.role === \"liar\").length,\n        normal: nonAdminUsers.filter((u)=>u.role === \"normal\").length,\n        unassigned: nonAdminUsers.filter((u)=>!u.role).length\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto py-8 px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow p-6 mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: \"Admin Panel\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: [\n                                            \"Welcome, \",\n                                            user.name\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleLogout,\n                                className: \"px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700\",\n                                children: \"Logout\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                    lineNumber: 159,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-sm font-medium text-yellow-800 mb-2\",\n                            children: \"Password Information:\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-yellow-700 space-y-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Admin user:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-mono bg-yellow-100 px-2 py-1 rounded\",\n                                            children: \"zxcvbnm\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>copyPassword(\"zxcvbnm\", \"admin-default\"),\n                                            className: \"text-yellow-600 hover:text-yellow-800 text-xs\",\n                                            children: copiedPassword === \"admin-default\" ? \"✓ Copied!\" : \"\\uD83D\\uDCCB Copy\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"All other users:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                            lineNumber: 189,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-mono bg-yellow-100 px-2 py-1 rounded\",\n                                            children: \"Random 8-character passwords\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-yellow-600\",\n                                            children: \"(see table below for individual passwords)\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                    lineNumber: 175,\n                    columnNumber: 9\n                }, this),\n                message && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-blue-50 border border-blue-200 rounded-md p-4 mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-blue-800\",\n                        children: message\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                        lineNumber: 199,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                    lineNumber: 198,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleSync,\n                            disabled: syncing,\n                            className: \"bg-blue-600 text-white p-4 rounded-lg hover:bg-blue-700 disabled:opacity-50\",\n                            children: syncing ? \"Syncing...\" : \"Sync Notion\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                            lineNumber: 205,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleClearDB,\n                            className: \"bg-red-600 text-white p-4 rounded-lg hover:bg-red-700\",\n                            children: \"Clear Database\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                            lineNumber: 213,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setShowNotes(!showNotes),\n                            className: \"bg-purple-600 text-white p-4 rounded-lg hover:bg-purple-700\",\n                            children: showNotes ? \"Hide Notes\" : \"View Notes (\".concat(notes.length, \")\")\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                    lineNumber: 204,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow p-6 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-900 mb-4\",\n                            children: \"Role Statistics\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                            lineNumber: 230,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-blue-600\",\n                                            children: roleStats.detective\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"Detective\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                            lineNumber: 234,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                    lineNumber: 232,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-red-600\",\n                                            children: roleStats.liar\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"Liar\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                            lineNumber: 238,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-green-600\",\n                                            children: roleStats.normal\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"Normal\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-gray-600\",\n                                            children: roleStats.unassigned\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                            lineNumber: 245,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"Unassigned\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                            lineNumber: 246,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                    lineNumber: 244,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                            lineNumber: 231,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                    lineNumber: 229,\n                    columnNumber: 9\n                }, this),\n                showNotes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow p-6 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-900 mb-4\",\n                            children: [\n                                \"User Notes (\",\n                                notes.length,\n                                \")\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                            lineNumber: 254,\n                            columnNumber: 13\n                        }, this),\n                        notes.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-500 text-center py-8\",\n                            children: \"No notes submitted yet.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                            lineNumber: 256,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4 max-h-96 overflow-y-auto\",\n                            children: notes.map((note)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border border-gray-200 rounded-lg p-4 bg-gray-50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-start mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-semibold text-gray-900\",\n                                                    children: note.user_name\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                                    lineNumber: 262,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: new Date(note.created_at).toLocaleString()\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                                    lineNumber: 263,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                            lineNumber: 261,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-700 whitespace-pre-wrap\",\n                                            children: note.content\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                            lineNumber: 267,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, note.id, true, {\n                                    fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 19\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                            lineNumber: 258,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                    lineNumber: 253,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-900 mb-4\",\n                            children: [\n                                \"All Users (\",\n                                allUsers.length,\n                                \")\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                            lineNumber: 277,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"overflow-x-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                className: \"min-w-full divide-y divide-gray-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                        className: \"bg-gray-50\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"Name\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                                    lineNumber: 282,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"Role\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                                    lineNumber: 283,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"Email\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                                    lineNumber: 284,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"Password\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                                    lineNumber: 285,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                            lineNumber: 281,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                        lineNumber: 280,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                        className: \"bg-white divide-y divide-gray-200\",\n                                        children: allUsers.map((user)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                                                        children: user.name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                                        lineNumber: 291,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"px-2 py-1 text-xs font-medium rounded-full \".concat(user.role === \"admin\" ? \"bg-purple-100 text-purple-800\" : user.role === \"detective\" ? \"bg-blue-100 text-blue-800\" : user.role === \"liar\" ? \"bg-red-100 text-red-800\" : user.role === \"normal\" ? \"bg-green-100 text-green-800\" : \"bg-gray-100 text-gray-800\"),\n                                                            children: user.role || \"Unassigned\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                                            lineNumber: 293,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                                        lineNumber: 292,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                        children: user.email || \"-\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                                        lineNumber: 303,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-mono bg-gray-100 px-2 py-1 rounded text-xs\",\n                                                                    children: user.displayPassword || \"Unknown\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                                                    lineNumber: 306,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>copyPassword(user.displayPassword || \"\", user.name),\n                                                                    className: \"text-blue-600 hover:text-blue-800 text-xs\",\n                                                                    title: \"Copy password\",\n                                                                    children: copiedPassword === user.name ? \"✓ Copied!\" : \"\\uD83D\\uDCCB Copy\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                                                    lineNumber: 309,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                                            lineNumber: 305,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                                        lineNumber: 304,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, user.id, true, {\n                                                fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                                lineNumber: 290,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                                lineNumber: 279,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                            lineNumber: 278,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n                    lineNumber: 276,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n            lineNumber: 157,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/admin/page.tsx\",\n        lineNumber: 156,\n        columnNumber: 5\n    }, this);\n}\n_s(AdminPage, \"2X5otNulpI0Hb8RQ587U07LHWcE=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = AdminPage;\nvar _c;\n$RefreshReg$(_c, \"AdminPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/admin/page.tsx\n"));

/***/ })

});