"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/notes/route";
exports.ids = ["app/api/notes/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "sqlite3":
/*!**************************!*\
  !*** external "sqlite3" ***!
  \**************************/
/***/ ((module) => {

module.exports = require("sqlite3");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnotes%2Froute&page=%2Fapi%2Fnotes%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnotes%2Froute.ts&appDir=%2FUsers%2Fadityabalasubramanian%2FDesktop%2Fdev%2Fpoy%2Fpoy-app%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fadityabalasubramanian%2FDesktop%2Fdev%2Fpoy%2Fpoy-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnotes%2Froute&page=%2Fapi%2Fnotes%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnotes%2Froute.ts&appDir=%2FUsers%2Fadityabalasubramanian%2FDesktop%2Fdev%2Fpoy%2Fpoy-app%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fadityabalasubramanian%2FDesktop%2Fdev%2Fpoy%2Fpoy-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var _Users_adityabalasubramanian_Desktop_dev_poy_poy_app_app_api_notes_route_ts__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./app/api/notes/route.ts */ \"(rsc)/./app/api/notes/route.ts\");\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/notes/route\",\n        pathname: \"/api/notes\",\n        filename: \"route\",\n        bundlePath: \"app/api/notes/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/api/notes/route.ts\",\n    nextConfigOutput,\n    userland: _Users_adityabalasubramanian_Desktop_dev_poy_poy_app_app_api_notes_route_ts__WEBPACK_IMPORTED_MODULE_2__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/notes/route\";\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnotes%2Froute&page=%2Fapi%2Fnotes%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnotes%2Froute.ts&appDir=%2FUsers%2Fadityabalasubramanian%2FDesktop%2Fdev%2Fpoy%2Fpoy-app%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fadityabalasubramanian%2FDesktop%2Fdev%2Fpoy%2Fpoy-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/notes/route.ts":
/*!********************************!*\
  !*** ./app/api/notes/route.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/database */ \"(rsc)/./lib/database.ts\");\n\n\n// Submit a note (for regular users)\nasync function POST(request) {\n    try {\n        const { userName, content } = await request.json();\n        if (!userName || !content) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                message: \"User name and content are required\"\n            }, {\n                status: 400\n            });\n        }\n        if (content.trim().length === 0) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                message: \"Note content cannot be empty\"\n            }, {\n                status: 400\n            });\n        }\n        if (content.length > 1000) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                message: \"Note content is too long (max 1000 characters)\"\n            }, {\n                status: 400\n            });\n        }\n        // Get user by name to get user ID\n        const user = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.getUserByName)(userName);\n        if (!user) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                message: \"User not found\"\n            }, {\n                status: 404\n            });\n        }\n        // Create the note\n        const result = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.createNote)(user.id, content.trim());\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: true,\n            message: \"Note submitted successfully\",\n            noteId: result.lastID\n        });\n    } catch (error) {\n        console.error(\"Submit note error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: false,\n            message: \"Failed to submit note\"\n        }, {\n            status: 500\n        });\n    }\n}\n// Get all notes (admin only)\nasync function GET(request) {\n    try {\n        const notes = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.getAllNotes)();\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: true,\n            notes: notes\n        });\n    } catch (error) {\n        console.error(\"Get notes error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: false,\n            message: \"Failed to get notes\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/notes/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/database.ts":
/*!*************************!*\
  !*** ./lib/database.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   assignRole: () => (/* binding */ assignRole),\n/* harmony export */   assignRolesToNonAdmins: () => (/* binding */ assignRolesToNonAdmins),\n/* harmony export */   assignStickyRolesToNonAdmins: () => (/* binding */ assignStickyRolesToNonAdmins),\n/* harmony export */   clearDatabase: () => (/* binding */ clearDatabase),\n/* harmony export */   clearNonAdminRoles: () => (/* binding */ clearNonAdminRoles),\n/* harmony export */   closeDatabase: () => (/* binding */ closeDatabase),\n/* harmony export */   createNote: () => (/* binding */ createNote),\n/* harmony export */   createUser: () => (/* binding */ createUser),\n/* harmony export */   getAllNotes: () => (/* binding */ getAllNotes),\n/* harmony export */   getAllUsers: () => (/* binding */ getAllUsers),\n/* harmony export */   getAllUsersWithRoles: () => (/* binding */ getAllUsersWithRoles),\n/* harmony export */   getDatabase: () => (/* binding */ getDatabase),\n/* harmony export */   getNonAdminUserCount: () => (/* binding */ getNonAdminUserCount),\n/* harmony export */   getRoleStats: () => (/* binding */ getRoleStats),\n/* harmony export */   getUserByEmail: () => (/* binding */ getUserByEmail),\n/* harmony export */   getUserByName: () => (/* binding */ getUserByName),\n/* harmony export */   getUserPasswords: () => (/* binding */ getUserPasswords),\n/* harmony export */   getUserRole: () => (/* binding */ getUserRole),\n/* harmony export */   getUserWithRole: () => (/* binding */ getUserWithRole),\n/* harmony export */   initDatabase: () => (/* binding */ initDatabase),\n/* harmony export */   userExists: () => (/* binding */ userExists)\n/* harmony export */ });\n/* harmony import */ var sqlite3__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! sqlite3 */ \"sqlite3\");\n/* harmony import */ var sqlite3__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(sqlite3__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst dbPath = path__WEBPACK_IMPORTED_MODULE_2___default().join(process.cwd(), \"poy.db\");\nlet db;\n// Generate random password\nfunction generateRandomPassword(length = 8) {\n    const chars = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789\";\n    let result = \"\";\n    for(let i = 0; i < length; i++){\n        result += chars.charAt(Math.floor(Math.random() * chars.length));\n    }\n    return result;\n}\nfunction migrateUsersTable(database) {\n    database.serialize(()=>{\n        // Create new table with correct constraints\n        database.run(`\n      CREATE TABLE users_new (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        name TEXT NOT NULL UNIQUE,\n        email TEXT UNIQUE,\n        phone TEXT,\n        about TEXT,\n        selfie TEXT,\n        password TEXT NOT NULL,\n        created_at DATETIME DEFAULT CURRENT_TIMESTAMP\n      )\n    `);\n        // Copy data from old table, handling potential duplicates\n        database.run(`\n      INSERT OR IGNORE INTO users_new (id, name, email, phone, about, selfie, password, created_at)\n      SELECT id, name, email, phone, about, selfie, password, created_at FROM users\n    `);\n        // Drop old table\n        database.run(\"DROP TABLE users\");\n        // Rename new table\n        database.run(\"ALTER TABLE users_new RENAME TO users\");\n        console.log(\"✅ Users table migration completed\");\n    });\n}\nfunction initDatabase() {\n    return new Promise((resolve, reject)=>{\n        if (db) {\n            resolve(db);\n            return;\n        }\n        db = new (sqlite3__WEBPACK_IMPORTED_MODULE_0___default().Database)(dbPath, (err)=>{\n            if (err) {\n                reject(err);\n                return;\n            }\n            // Create tables\n            db.serialize(()=>{\n                // Check if we need to migrate the users table\n                db.get(\"SELECT sql FROM sqlite_master WHERE type='table' AND name='users'\", (err, row)=>{\n                    if (err) {\n                        console.error(\"Error checking table schema:\", err);\n                        return;\n                    }\n                    // If table exists but doesn't have UNIQUE constraint on name, migrate it\n                    if (row && row.sql && !row.sql.includes(\"name TEXT NOT NULL UNIQUE\")) {\n                        console.log(\"Migrating users table to add UNIQUE constraint on name...\");\n                        migrateUsersTable(db);\n                    } else {\n                        // Create users table with proper constraints\n                        db.run(`\n              CREATE TABLE IF NOT EXISTS users (\n                id INTEGER PRIMARY KEY AUTOINCREMENT,\n                name TEXT NOT NULL UNIQUE,\n                email TEXT UNIQUE,\n                phone TEXT,\n                about TEXT,\n                selfie TEXT,\n                password TEXT NOT NULL,\n                created_at DATETIME DEFAULT CURRENT_TIMESTAMP\n              )\n            `);\n                    }\n                });\n                // Create roles table\n                db.run(`\n          CREATE TABLE IF NOT EXISTS roles (\n            id INTEGER PRIMARY KEY AUTOINCREMENT,\n            user_id INTEGER NOT NULL,\n            role TEXT NOT NULL CHECK (role IN ('detective', 'liar', 'normal', 'admin')),\n            assigned_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n            FOREIGN KEY (user_id) REFERENCES users (id)\n          )\n        `);\n                // Create notes table\n                db.run(`\n          CREATE TABLE IF NOT EXISTS notes (\n            id INTEGER PRIMARY KEY AUTOINCREMENT,\n            user_id INTEGER NOT NULL,\n            content TEXT NOT NULL,\n            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n            FOREIGN KEY (user_id) REFERENCES users (id)\n          )\n        `);\n                // Create user_passwords table to store plain passwords for admin viewing\n                db.run(`\n          CREATE TABLE IF NOT EXISTS user_passwords (\n            id INTEGER PRIMARY KEY AUTOINCREMENT,\n            user_id INTEGER NOT NULL UNIQUE,\n            plain_password TEXT NOT NULL,\n            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n            FOREIGN KEY (user_id) REFERENCES users (id)\n          )\n        `);\n                // Create admin user if it doesn't exist and ensure admin role\n                db.get(\"SELECT id FROM users WHERE name = ?\", [\n                    \"admin\"\n                ], (err, row)=>{\n                    if (err) {\n                        console.error(\"Error checking admin user:\", err);\n                        return;\n                    }\n                    if (!row) {\n                        // Create admin user\n                        const hashedPassword = bcryptjs__WEBPACK_IMPORTED_MODULE_1___default().hashSync(\"zxcvbnm\", 10);\n                        db.run(\"INSERT INTO users (name, password) VALUES (?, ?)\", [\n                            \"admin\",\n                            hashedPassword\n                        ], function(err) {\n                            if (err) {\n                                console.error(\"Error creating admin user:\", err);\n                                return;\n                            }\n                            // Assign admin role\n                            db.run(\"INSERT INTO roles (user_id, role) VALUES (?, ?)\", [\n                                this.lastID,\n                                \"admin\"\n                            ]);\n                            console.log(\"Admin user created with password: zxcvbnm\");\n                        });\n                    } else {\n                        // Admin user exists, ensure they have admin role\n                        db.get(\"SELECT id FROM roles WHERE user_id = ? AND role = ?\", [\n                            row.id,\n                            \"admin\"\n                        ], (err, roleRow)=>{\n                            if (err) {\n                                console.error(\"Error checking admin role:\", err);\n                                return;\n                            }\n                            if (!roleRow) {\n                                // Remove any existing role and assign admin role\n                                db.run(\"DELETE FROM roles WHERE user_id = ?\", [\n                                    row.id\n                                ], (err)=>{\n                                    if (err) {\n                                        console.error(\"Error removing existing admin roles:\", err);\n                                        return;\n                                    }\n                                    db.run(\"INSERT INTO roles (user_id, role) VALUES (?, ?)\", [\n                                        row.id,\n                                        \"admin\"\n                                    ], (err)=>{\n                                        if (err) {\n                                            console.error(\"Error assigning admin role:\", err);\n                                        } else {\n                                            console.log(\"Admin role assigned to existing admin user\");\n                                        }\n                                    });\n                                });\n                            }\n                        });\n                    }\n                });\n                resolve(db);\n            });\n        });\n    });\n}\nfunction getDatabase() {\n    if (db) {\n        return Promise.resolve(db);\n    }\n    return initDatabase();\n}\nfunction closeDatabase() {\n    if (db) {\n        db.close();\n    }\n}\n// User functions\nfunction createUser(name, email, phone, about, selfie) {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            const plainPassword = generateRandomPassword(8);\n            const hashedPassword = bcryptjs__WEBPACK_IMPORTED_MODULE_1___default().hashSync(plainPassword, 10);\n            db.run(\"INSERT INTO users (name, email, phone, about, selfie, password) VALUES (?, ?, ?, ?, ?, ?)\", [\n                name,\n                email,\n                phone,\n                about,\n                selfie,\n                hashedPassword\n            ], function(err) {\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                const userId = this.lastID;\n                // Store plain password for admin viewing\n                db.run(\"INSERT INTO user_passwords (user_id, plain_password) VALUES (?, ?)\", [\n                    userId,\n                    plainPassword\n                ], (err)=>{\n                    if (err) {\n                        console.error(\"Error storing plain password:\", err);\n                    // Don't reject here, user creation was successful\n                    }\n                    // Assign normal role to new user\n                    db.run(\"INSERT INTO roles (user_id, role) VALUES (?, ?)\", [\n                        userId,\n                        \"normal\"\n                    ], (err)=>{\n                        if (err) {\n                            console.error(\"Error assigning normal role to new user:\", err);\n                        // Don't reject here, user creation was successful\n                        }\n                        resolve({\n                            lastID: userId,\n                            password: plainPassword\n                        });\n                    });\n                });\n            });\n        }).catch(reject);\n    });\n}\nfunction getUserByName(name) {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            db.get(\"SELECT * FROM users WHERE name = ?\", [\n                name\n            ], (err, row)=>{\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                resolve(row);\n            });\n        }).catch(reject);\n    });\n}\nfunction getUserByEmail(email) {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            db.get(\"SELECT * FROM users WHERE email = ?\", [\n                email\n            ], (err, row)=>{\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                resolve(row);\n            });\n        }).catch(reject);\n    });\n}\nfunction userExists(name, email) {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            db.get(\"SELECT id FROM users WHERE name = ? OR email = ?\", [\n                name,\n                email\n            ], (err, row)=>{\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                resolve(!!row);\n            });\n        }).catch(reject);\n    });\n}\nfunction getAllUsers() {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            db.all(\"SELECT * FROM users ORDER BY name\", (err, rows)=>{\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                resolve(rows);\n            });\n        }).catch(reject);\n    });\n}\nfunction getUserWithRole(name) {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            db.get(`\n        SELECT u.*, r.role \n        FROM users u \n        LEFT JOIN roles r ON u.id = r.user_id \n        WHERE u.name = ?\n      `, [\n                name\n            ], (err, row)=>{\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                resolve(row);\n            });\n        }).catch(reject);\n    });\n}\nfunction getAllUsersWithRoles() {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            db.all(`\n        SELECT u.*, r.role \n        FROM users u \n        LEFT JOIN roles r ON u.id = r.user_id \n        ORDER BY u.name\n      `, (err, rows)=>{\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                resolve(rows);\n            });\n        }).catch(reject);\n    });\n}\n// Role functions\nfunction assignRole(userId, role) {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            db.serialize(()=>{\n                // Remove existing role\n                db.run(\"DELETE FROM roles WHERE user_id = ?\", [\n                    userId\n                ], (err)=>{\n                    if (err) {\n                        reject(err);\n                        return;\n                    }\n                    // Assign new role\n                    db.run(\"INSERT INTO roles (user_id, role) VALUES (?, ?)\", [\n                        userId,\n                        role\n                    ], (err)=>{\n                        if (err) {\n                            reject(err);\n                            return;\n                        }\n                        resolve();\n                    });\n                });\n            });\n        }).catch(reject);\n    });\n}\nfunction getUserRole(userId) {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            db.get(\"SELECT role FROM roles WHERE user_id = ?\", [\n                userId\n            ], (err, row)=>{\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                resolve(row ? row.role : null);\n            });\n        }).catch(reject);\n    });\n}\nfunction clearNonAdminRoles() {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            db.run('DELETE FROM roles WHERE role != \"admin\"', (err)=>{\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                resolve();\n            });\n        }).catch(reject);\n    });\n}\nfunction getRoleStats() {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            db.all(`\n        SELECT role, COUNT(*) as count \n        FROM roles \n        GROUP BY role\n      `, (err, rows)=>{\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                const result = {\n                    detective: 0,\n                    liar: 0,\n                    normal: 0,\n                    admin: 0\n                };\n                rows.forEach((row)=>{\n                    result[row.role] = row.count;\n                });\n                resolve(result);\n            });\n        }).catch(reject);\n    });\n}\nfunction getNonAdminUserCount() {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            db.get(`\n        SELECT COUNT(*) as count FROM users u\n        LEFT JOIN roles r ON u.id = r.user_id\n        WHERE r.role IS NULL OR r.role != 'admin'\n      `, (err, row)=>{\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                resolve(row.count);\n            });\n        }).catch(reject);\n    });\n}\nfunction assignRolesToNonAdmins() {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            // Get all users without admin role\n            db.all(`\n        SELECT u.id, u.name FROM users u\n        LEFT JOIN roles r ON u.id = r.user_id\n        WHERE r.role IS NULL OR r.role != 'admin'\n      `, async (err, rows)=>{\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                if (rows.length < 3) {\n                    resolve({\n                        message: `Need at least 3 non-admin users to assign roles (found ${rows.length})`\n                    });\n                    return;\n                }\n                try {\n                    // Clear existing non-admin roles\n                    await clearNonAdminRoles();\n                    // Shuffle users randomly\n                    const shuffled = [\n                        ...rows\n                    ].sort(()=>Math.random() - 0.5);\n                    // Assign exactly 1 detective, 1 liar, and everyone else as normal\n                    await assignRole(shuffled[0].id, \"detective\");\n                    await assignRole(shuffled[1].id, \"liar\");\n                    // Assign normal role to everyone else\n                    for(let i = 2; i < shuffled.length; i++){\n                        await assignRole(shuffled[i].id, \"normal\");\n                    }\n                    resolve({\n                        message: `Roles assigned successfully: 1 detective, 1 liar, ${shuffled.length - 2} normal users`\n                    });\n                } catch (error) {\n                    reject(error);\n                }\n            });\n        }).catch(reject);\n    });\n}\nfunction assignStickyRolesToNonAdmins() {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            // Get all users with their current roles\n            db.all(`\n        SELECT u.id, u.name, r.role FROM users u\n        LEFT JOIN roles r ON u.id = r.user_id\n        WHERE r.role IS NULL OR r.role != 'admin'\n        ORDER BY u.id\n      `, async (err, rows)=>{\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                if (rows.length < 5) {\n                    resolve({\n                        message: `Need at least 5 non-admin users to assign roles (found ${rows.length})`\n                    });\n                    return;\n                }\n                try {\n                    // Count existing special roles\n                    const existingDetectives = rows.filter((u)=>u.role === \"detective\");\n                    const existingLiars = rows.filter((u)=>u.role === \"liar\");\n                    let detectivesNeeded = 0;\n                    let liarsNeeded = 0;\n                    // Determine how many detectives and liars we need based on user count\n                    if (rows.length >= 9) {\n                        // 9+ users: 2 detectives, 2 liars\n                        detectivesNeeded = 2 - existingDetectives.length;\n                        liarsNeeded = 2 - existingLiars.length;\n                    } else {\n                        // 5-8 users: 1 detective, 1 liar\n                        detectivesNeeded = 1 - existingDetectives.length;\n                        liarsNeeded = 1 - existingLiars.length;\n                    }\n                    let newAssignments = 0;\n                    // First, assign normal role to any unassigned users\n                    const unassignedUsers = rows.filter((u)=>!u.role);\n                    for (const user of unassignedUsers){\n                        await assignRole(user.id, \"normal\");\n                        newAssignments++;\n                    }\n                    // If we need more detectives or liars, promote from normal users\n                    if (detectivesNeeded > 0 || liarsNeeded > 0) {\n                        // Get users who are currently normal (including newly assigned ones)\n                        const availableNormalUsers = rows.filter((u)=>u.role === \"normal\" || !u.role);\n                        const shuffledNormal = [\n                            ...availableNormalUsers\n                        ].sort(()=>Math.random() - 0.5);\n                        let promotionIndex = 0;\n                        // Assign needed detectives\n                        for(let i = 0; i < detectivesNeeded && promotionIndex < shuffledNormal.length; i++){\n                            await assignRole(shuffledNormal[promotionIndex].id, \"detective\");\n                            promotionIndex++;\n                            newAssignments++;\n                        }\n                        // Assign needed liars\n                        for(let i = 0; i < liarsNeeded && promotionIndex < shuffledNormal.length; i++){\n                            await assignRole(shuffledNormal[promotionIndex].id, \"liar\");\n                            promotionIndex++;\n                            newAssignments++;\n                        }\n                    }\n                    const finalDetectives = existingDetectives.length + Math.max(0, detectivesNeeded);\n                    const finalLiars = existingLiars.length + Math.max(0, liarsNeeded);\n                    const finalNormal = rows.length - finalDetectives - finalLiars;\n                    resolve({\n                        message: `Roles assigned: ${finalDetectives} detective(s), ${finalLiars} liar(s), ${finalNormal} normal users (${newAssignments} new assignments)`\n                    });\n                } catch (error) {\n                    reject(error);\n                }\n            });\n        }).catch(reject);\n    });\n}\n// Notes functions\nfunction createNote(userId, content) {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            db.run(\"INSERT INTO notes (user_id, content) VALUES (?, ?)\", [\n                userId,\n                content\n            ], function(err) {\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                resolve({\n                    lastID: this.lastID\n                });\n            });\n        }).catch(reject);\n    });\n}\nfunction getAllNotes() {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            db.all(`\n        SELECT n.*, u.name as user_name\n        FROM notes n\n        JOIN users u ON n.user_id = u.id\n        ORDER BY n.created_at DESC\n      `, (err, rows)=>{\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                resolve(rows);\n            });\n        }).catch(reject);\n    });\n}\nfunction getUserPasswords() {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            db.all(`\n        SELECT up.*, u.name as user_name\n        FROM user_passwords up\n        JOIN users u ON up.user_id = u.id\n        ORDER BY u.name\n      `, (err, rows)=>{\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                resolve(rows);\n            });\n        }).catch(reject);\n    });\n}\n// Clear all data except admin\nfunction clearDatabase() {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            db.serialize(()=>{\n                db.run(\"DELETE FROM notes\", (err)=>{\n                    if (err) {\n                        reject(err);\n                        return;\n                    }\n                    db.run('DELETE FROM user_passwords WHERE user_id != (SELECT id FROM users WHERE name = \"admin\")', (err)=>{\n                        if (err) {\n                            reject(err);\n                            return;\n                        }\n                        db.run('DELETE FROM roles WHERE role != \"admin\"', (err)=>{\n                            if (err) {\n                                reject(err);\n                                return;\n                            }\n                            db.run('DELETE FROM users WHERE name != \"admin\"', (err)=>{\n                                if (err) {\n                                    reject(err);\n                                    return;\n                                }\n                                resolve({\n                                    message: \"Database cleared successfully\"\n                                });\n                            });\n                        });\n                    });\n                });\n            });\n        }).catch(reject);\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/database.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/bcryptjs"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnotes%2Froute&page=%2Fapi%2Fnotes%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnotes%2Froute.ts&appDir=%2FUsers%2Fadityabalasubramanian%2FDesktop%2Fdev%2Fpoy%2Fpoy-app%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fadityabalasubramanian%2FDesktop%2Fdev%2Fpoy%2Fpoy-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();