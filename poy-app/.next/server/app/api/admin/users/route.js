"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/admin/users/route";
exports.ids = ["app/api/admin/users/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "sqlite3":
/*!**************************!*\
  !*** external "sqlite3" ***!
  \**************************/
/***/ ((module) => {

module.exports = require("sqlite3");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fusers%2Froute&page=%2Fapi%2Fadmin%2Fusers%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fusers%2Froute.ts&appDir=%2FUsers%2Fadityabalasubramanian%2FDesktop%2Fdev%2Fpoy%2Fpoy-app%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fadityabalasubramanian%2FDesktop%2Fdev%2Fpoy%2Fpoy-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fusers%2Froute&page=%2Fapi%2Fadmin%2Fusers%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fusers%2Froute.ts&appDir=%2FUsers%2Fadityabalasubramanian%2FDesktop%2Fdev%2Fpoy%2Fpoy-app%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fadityabalasubramanian%2FDesktop%2Fdev%2Fpoy%2Fpoy-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var _Users_adityabalasubramanian_Desktop_dev_poy_poy_app_app_api_admin_users_route_ts__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./app/api/admin/users/route.ts */ \"(rsc)/./app/api/admin/users/route.ts\");\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/admin/users/route\",\n        pathname: \"/api/admin/users\",\n        filename: \"route\",\n        bundlePath: \"app/api/admin/users/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/api/admin/users/route.ts\",\n    nextConfigOutput,\n    userland: _Users_adityabalasubramanian_Desktop_dev_poy_poy_app_app_api_admin_users_route_ts__WEBPACK_IMPORTED_MODULE_2__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/admin/users/route\";\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fusers%2Froute&page=%2Fapi%2Fadmin%2Fusers%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fusers%2Froute.ts&appDir=%2FUsers%2Fadityabalasubramanian%2FDesktop%2Fdev%2Fpoy%2Fpoy-app%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fadityabalasubramanian%2FDesktop%2Fdev%2Fpoy%2Fpoy-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/admin/users/route.ts":
/*!**************************************!*\
  !*** ./app/api/admin/users/route.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/database */ \"(rsc)/./lib/database.ts\");\n\n\nasync function GET(request) {\n    try {\n        // Get all users with their roles and actual passwords (admin only endpoint)\n        const users = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.getAllUsersWithRoles)();\n        const passwords = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.getUserPasswords)();\n        // Create a map of user_id to password\n        const passwordMap = new Map();\n        passwords.forEach((p)=>{\n            passwordMap.set(p.user_id, p.plain_password);\n        });\n        // Add actual passwords to users\n        const usersWithPasswords = users.map((user)=>{\n            let displayPassword = \"Unknown\";\n            // Get actual password from database or use default for admin\n            if (user.name === \"admin\") {\n                displayPassword = \"zxcvbnm\";\n            } else {\n                displayPassword = passwordMap.get(user.id) || \"Not found\";\n            }\n            return {\n                ...user,\n                displayPassword\n            };\n        });\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: true,\n            users: usersWithPasswords\n        });\n    } catch (error) {\n        console.error(\"Get admin users error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: false,\n            message: \"Failed to get users with passwords\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/admin/users/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/database.ts":
/*!*************************!*\
  !*** ./lib/database.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   assignRole: () => (/* binding */ assignRole),\n/* harmony export */   assignRolesToNonAdmins: () => (/* binding */ assignRolesToNonAdmins),\n/* harmony export */   assignStickyRolesToNonAdmins: () => (/* binding */ assignStickyRolesToNonAdmins),\n/* harmony export */   clearDatabase: () => (/* binding */ clearDatabase),\n/* harmony export */   clearNonAdminRoles: () => (/* binding */ clearNonAdminRoles),\n/* harmony export */   closeDatabase: () => (/* binding */ closeDatabase),\n/* harmony export */   createNote: () => (/* binding */ createNote),\n/* harmony export */   createUser: () => (/* binding */ createUser),\n/* harmony export */   getAllNotes: () => (/* binding */ getAllNotes),\n/* harmony export */   getAllUsers: () => (/* binding */ getAllUsers),\n/* harmony export */   getAllUsersWithRoles: () => (/* binding */ getAllUsersWithRoles),\n/* harmony export */   getDatabase: () => (/* binding */ getDatabase),\n/* harmony export */   getNonAdminUserCount: () => (/* binding */ getNonAdminUserCount),\n/* harmony export */   getRoleStats: () => (/* binding */ getRoleStats),\n/* harmony export */   getUserByEmail: () => (/* binding */ getUserByEmail),\n/* harmony export */   getUserByName: () => (/* binding */ getUserByName),\n/* harmony export */   getUserPasswords: () => (/* binding */ getUserPasswords),\n/* harmony export */   getUserRole: () => (/* binding */ getUserRole),\n/* harmony export */   getUserWithRole: () => (/* binding */ getUserWithRole),\n/* harmony export */   initDatabase: () => (/* binding */ initDatabase),\n/* harmony export */   userExists: () => (/* binding */ userExists)\n/* harmony export */ });\n/* harmony import */ var sqlite3__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! sqlite3 */ \"sqlite3\");\n/* harmony import */ var sqlite3__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(sqlite3__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst dbPath = path__WEBPACK_IMPORTED_MODULE_2___default().join(process.cwd(), \"poy.db\");\nlet db;\n// Generate random password\nfunction generateRandomPassword(length = 8) {\n    const chars = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789\";\n    let result = \"\";\n    for(let i = 0; i < length; i++){\n        result += chars.charAt(Math.floor(Math.random() * chars.length));\n    }\n    return result;\n}\nfunction migrateUsersTable(database) {\n    database.serialize(()=>{\n        // Create new table with correct constraints\n        database.run(`\n      CREATE TABLE users_new (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        name TEXT NOT NULL UNIQUE,\n        email TEXT UNIQUE,\n        phone TEXT,\n        about TEXT,\n        selfie TEXT,\n        password TEXT NOT NULL,\n        created_at DATETIME DEFAULT CURRENT_TIMESTAMP\n      )\n    `);\n        // Copy data from old table, handling potential duplicates\n        database.run(`\n      INSERT OR IGNORE INTO users_new (id, name, email, phone, about, selfie, password, created_at)\n      SELECT id, name, email, phone, about, selfie, password, created_at FROM users\n    `);\n        // Drop old table\n        database.run(\"DROP TABLE users\");\n        // Rename new table\n        database.run(\"ALTER TABLE users_new RENAME TO users\");\n        console.log(\"✅ Users table migration completed\");\n    });\n}\nfunction initDatabase() {\n    return new Promise((resolve, reject)=>{\n        if (db) {\n            resolve(db);\n            return;\n        }\n        db = new (sqlite3__WEBPACK_IMPORTED_MODULE_0___default().Database)(dbPath, (err)=>{\n            if (err) {\n                reject(err);\n                return;\n            }\n            // Create tables\n            db.serialize(()=>{\n                // Check if we need to migrate the users table\n                db.get(\"SELECT sql FROM sqlite_master WHERE type='table' AND name='users'\", (err, row)=>{\n                    if (err) {\n                        console.error(\"Error checking table schema:\", err);\n                        return;\n                    }\n                    // If table exists but doesn't have UNIQUE constraint on name, migrate it\n                    if (row && row.sql && !row.sql.includes(\"name TEXT NOT NULL UNIQUE\")) {\n                        console.log(\"Migrating users table to add UNIQUE constraint on name...\");\n                        migrateUsersTable(db);\n                    } else {\n                        // Create users table with proper constraints\n                        db.run(`\n              CREATE TABLE IF NOT EXISTS users (\n                id INTEGER PRIMARY KEY AUTOINCREMENT,\n                name TEXT NOT NULL UNIQUE,\n                email TEXT UNIQUE,\n                phone TEXT,\n                about TEXT,\n                selfie TEXT,\n                password TEXT NOT NULL,\n                created_at DATETIME DEFAULT CURRENT_TIMESTAMP\n              )\n            `);\n                    }\n                });\n                // Create roles table\n                db.run(`\n          CREATE TABLE IF NOT EXISTS roles (\n            id INTEGER PRIMARY KEY AUTOINCREMENT,\n            user_id INTEGER NOT NULL,\n            role TEXT NOT NULL CHECK (role IN ('detective', 'liar', 'normal', 'admin')),\n            assigned_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n            FOREIGN KEY (user_id) REFERENCES users (id)\n          )\n        `);\n                // Create notes table\n                db.run(`\n          CREATE TABLE IF NOT EXISTS notes (\n            id INTEGER PRIMARY KEY AUTOINCREMENT,\n            user_id INTEGER NOT NULL,\n            content TEXT NOT NULL,\n            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n            FOREIGN KEY (user_id) REFERENCES users (id)\n          )\n        `);\n                // Create user_passwords table to store plain passwords for admin viewing\n                db.run(`\n          CREATE TABLE IF NOT EXISTS user_passwords (\n            id INTEGER PRIMARY KEY AUTOINCREMENT,\n            user_id INTEGER NOT NULL UNIQUE,\n            plain_password TEXT NOT NULL,\n            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n            FOREIGN KEY (user_id) REFERENCES users (id)\n          )\n        `);\n                // Create admin user if it doesn't exist and ensure admin role\n                db.get(\"SELECT id FROM users WHERE name = ?\", [\n                    \"admin\"\n                ], (err, row)=>{\n                    if (err) {\n                        console.error(\"Error checking admin user:\", err);\n                        return;\n                    }\n                    if (!row) {\n                        // Create admin user\n                        const hashedPassword = bcryptjs__WEBPACK_IMPORTED_MODULE_1___default().hashSync(\"zxcvbnm\", 10);\n                        db.run(\"INSERT INTO users (name, password) VALUES (?, ?)\", [\n                            \"admin\",\n                            hashedPassword\n                        ], function(err) {\n                            if (err) {\n                                console.error(\"Error creating admin user:\", err);\n                                return;\n                            }\n                            // Assign admin role\n                            db.run(\"INSERT INTO roles (user_id, role) VALUES (?, ?)\", [\n                                this.lastID,\n                                \"admin\"\n                            ]);\n                            console.log(\"Admin user created with password: zxcvbnm\");\n                        });\n                    } else {\n                        // Admin user exists, ensure they have admin role\n                        db.get(\"SELECT id FROM roles WHERE user_id = ? AND role = ?\", [\n                            row.id,\n                            \"admin\"\n                        ], (err, roleRow)=>{\n                            if (err) {\n                                console.error(\"Error checking admin role:\", err);\n                                return;\n                            }\n                            if (!roleRow) {\n                                // Remove any existing role and assign admin role\n                                db.run(\"DELETE FROM roles WHERE user_id = ?\", [\n                                    row.id\n                                ], (err)=>{\n                                    if (err) {\n                                        console.error(\"Error removing existing admin roles:\", err);\n                                        return;\n                                    }\n                                    db.run(\"INSERT INTO roles (user_id, role) VALUES (?, ?)\", [\n                                        row.id,\n                                        \"admin\"\n                                    ], (err)=>{\n                                        if (err) {\n                                            console.error(\"Error assigning admin role:\", err);\n                                        } else {\n                                            console.log(\"Admin role assigned to existing admin user\");\n                                        }\n                                    });\n                                });\n                            }\n                        });\n                    }\n                });\n                resolve(db);\n            });\n        });\n    });\n}\nfunction getDatabase() {\n    if (db) {\n        return Promise.resolve(db);\n    }\n    return initDatabase();\n}\nfunction closeDatabase() {\n    if (db) {\n        db.close();\n    }\n}\n// User functions\nfunction createUser(name, email, phone, about, selfie) {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            const plainPassword = generateRandomPassword(8);\n            const hashedPassword = bcryptjs__WEBPACK_IMPORTED_MODULE_1___default().hashSync(plainPassword, 10);\n            db.run(\"INSERT INTO users (name, email, phone, about, selfie, password) VALUES (?, ?, ?, ?, ?, ?)\", [\n                name,\n                email,\n                phone,\n                about,\n                selfie,\n                hashedPassword\n            ], function(err) {\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                // Store plain password for admin viewing\n                db.run(\"INSERT INTO user_passwords (user_id, plain_password) VALUES (?, ?)\", [\n                    this.lastID,\n                    plainPassword\n                ], (err)=>{\n                    if (err) {\n                        console.error(\"Error storing plain password:\", err);\n                    // Don't reject here, user creation was successful\n                    }\n                    resolve({\n                        lastID: this.lastID,\n                        password: plainPassword\n                    });\n                });\n            });\n        }).catch(reject);\n    });\n}\nfunction getUserByName(name) {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            db.get(\"SELECT * FROM users WHERE name = ?\", [\n                name\n            ], (err, row)=>{\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                resolve(row);\n            });\n        }).catch(reject);\n    });\n}\nfunction getUserByEmail(email) {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            db.get(\"SELECT * FROM users WHERE email = ?\", [\n                email\n            ], (err, row)=>{\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                resolve(row);\n            });\n        }).catch(reject);\n    });\n}\nfunction userExists(name, email) {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            db.get(\"SELECT id FROM users WHERE name = ? OR email = ?\", [\n                name,\n                email\n            ], (err, row)=>{\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                resolve(!!row);\n            });\n        }).catch(reject);\n    });\n}\nfunction getAllUsers() {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            db.all(\"SELECT * FROM users ORDER BY name\", (err, rows)=>{\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                resolve(rows);\n            });\n        }).catch(reject);\n    });\n}\nfunction getUserWithRole(name) {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            db.get(`\n        SELECT u.*, r.role \n        FROM users u \n        LEFT JOIN roles r ON u.id = r.user_id \n        WHERE u.name = ?\n      `, [\n                name\n            ], (err, row)=>{\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                resolve(row);\n            });\n        }).catch(reject);\n    });\n}\nfunction getAllUsersWithRoles() {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            db.all(`\n        SELECT u.*, r.role \n        FROM users u \n        LEFT JOIN roles r ON u.id = r.user_id \n        ORDER BY u.name\n      `, (err, rows)=>{\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                resolve(rows);\n            });\n        }).catch(reject);\n    });\n}\n// Role functions\nfunction assignRole(userId, role) {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            db.serialize(()=>{\n                // Remove existing role\n                db.run(\"DELETE FROM roles WHERE user_id = ?\", [\n                    userId\n                ], (err)=>{\n                    if (err) {\n                        reject(err);\n                        return;\n                    }\n                    // Assign new role\n                    db.run(\"INSERT INTO roles (user_id, role) VALUES (?, ?)\", [\n                        userId,\n                        role\n                    ], (err)=>{\n                        if (err) {\n                            reject(err);\n                            return;\n                        }\n                        resolve();\n                    });\n                });\n            });\n        }).catch(reject);\n    });\n}\nfunction getUserRole(userId) {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            db.get(\"SELECT role FROM roles WHERE user_id = ?\", [\n                userId\n            ], (err, row)=>{\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                resolve(row ? row.role : null);\n            });\n        }).catch(reject);\n    });\n}\nfunction clearNonAdminRoles() {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            db.run('DELETE FROM roles WHERE role != \"admin\"', (err)=>{\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                resolve();\n            });\n        }).catch(reject);\n    });\n}\nfunction getRoleStats() {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            db.all(`\n        SELECT role, COUNT(*) as count \n        FROM roles \n        GROUP BY role\n      `, (err, rows)=>{\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                const result = {\n                    detective: 0,\n                    liar: 0,\n                    normal: 0,\n                    admin: 0\n                };\n                rows.forEach((row)=>{\n                    result[row.role] = row.count;\n                });\n                resolve(result);\n            });\n        }).catch(reject);\n    });\n}\nfunction getNonAdminUserCount() {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            db.get(`\n        SELECT COUNT(*) as count FROM users u\n        LEFT JOIN roles r ON u.id = r.user_id\n        WHERE r.role IS NULL OR r.role != 'admin'\n      `, (err, row)=>{\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                resolve(row.count);\n            });\n        }).catch(reject);\n    });\n}\nfunction assignRolesToNonAdmins() {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            // Get all users without admin role\n            db.all(`\n        SELECT u.id, u.name FROM users u\n        LEFT JOIN roles r ON u.id = r.user_id\n        WHERE r.role IS NULL OR r.role != 'admin'\n      `, async (err, rows)=>{\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                if (rows.length < 3) {\n                    resolve({\n                        message: `Need at least 3 non-admin users to assign roles (found ${rows.length})`\n                    });\n                    return;\n                }\n                try {\n                    // Clear existing non-admin roles\n                    await clearNonAdminRoles();\n                    // Shuffle users randomly\n                    const shuffled = [\n                        ...rows\n                    ].sort(()=>Math.random() - 0.5);\n                    // Assign exactly 1 detective, 1 liar, and everyone else as normal\n                    await assignRole(shuffled[0].id, \"detective\");\n                    await assignRole(shuffled[1].id, \"liar\");\n                    // Assign normal role to everyone else\n                    for(let i = 2; i < shuffled.length; i++){\n                        await assignRole(shuffled[i].id, \"normal\");\n                    }\n                    resolve({\n                        message: `Roles assigned successfully: 1 detective, 1 liar, ${shuffled.length - 2} normal users`\n                    });\n                } catch (error) {\n                    reject(error);\n                }\n            });\n        }).catch(reject);\n    });\n}\nfunction assignStickyRolesToNonAdmins() {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            // Get all users with their current roles\n            db.all(`\n        SELECT u.id, u.name, r.role FROM users u\n        LEFT JOIN roles r ON u.id = r.user_id\n        WHERE r.role IS NULL OR r.role != 'admin'\n        ORDER BY u.id\n      `, async (err, rows)=>{\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                if (rows.length < 5) {\n                    resolve({\n                        message: `Need at least 5 non-admin users to assign roles (found ${rows.length})`\n                    });\n                    return;\n                }\n                try {\n                    // Count existing special roles\n                    const existingDetectives = rows.filter((u)=>u.role === \"detective\");\n                    const existingLiars = rows.filter((u)=>u.role === \"liar\");\n                    const usersWithoutRoles = rows.filter((u)=>!u.role);\n                    let detectivesNeeded = 0;\n                    let liarsNeeded = 0;\n                    // Determine how many detectives and liars we need based on user count\n                    if (rows.length >= 9) {\n                        // 9+ users: 2 detectives, 2 liars\n                        detectivesNeeded = 2 - existingDetectives.length;\n                        liarsNeeded = 2 - existingLiars.length;\n                    } else {\n                        // 5-8 users: 1 detective, 1 liar\n                        detectivesNeeded = 1 - existingDetectives.length;\n                        liarsNeeded = 1 - existingLiars.length;\n                    }\n                    // Shuffle users without roles for random assignment\n                    const shuffledUnassigned = [\n                        ...usersWithoutRoles\n                    ].sort(()=>Math.random() - 0.5);\n                    let assignmentIndex = 0;\n                    let newAssignments = 0;\n                    // Assign needed detectives\n                    for(let i = 0; i < detectivesNeeded && assignmentIndex < shuffledUnassigned.length; i++){\n                        await assignRole(shuffledUnassigned[assignmentIndex].id, \"detective\");\n                        assignmentIndex++;\n                        newAssignments++;\n                    }\n                    // Assign needed liars\n                    for(let i = 0; i < liarsNeeded && assignmentIndex < shuffledUnassigned.length; i++){\n                        await assignRole(shuffledUnassigned[assignmentIndex].id, \"liar\");\n                        assignmentIndex++;\n                        newAssignments++;\n                    }\n                    // Assign normal role to remaining unassigned users\n                    for(let i = assignmentIndex; i < shuffledUnassigned.length; i++){\n                        await assignRole(shuffledUnassigned[i].id, \"normal\");\n                        newAssignments++;\n                    }\n                    const finalDetectives = existingDetectives.length + Math.max(0, detectivesNeeded);\n                    const finalLiars = existingLiars.length + Math.max(0, liarsNeeded);\n                    const finalNormal = rows.length - finalDetectives - finalLiars;\n                    resolve({\n                        message: `Roles assigned: ${finalDetectives} detective(s), ${finalLiars} liar(s), ${finalNormal} normal users (${newAssignments} new assignments)`\n                    });\n                } catch (error) {\n                    reject(error);\n                }\n            });\n        }).catch(reject);\n    });\n}\n// Notes functions\nfunction createNote(userId, content) {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            db.run(\"INSERT INTO notes (user_id, content) VALUES (?, ?)\", [\n                userId,\n                content\n            ], function(err) {\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                resolve({\n                    lastID: this.lastID\n                });\n            });\n        }).catch(reject);\n    });\n}\nfunction getAllNotes() {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            db.all(`\n        SELECT n.*, u.name as user_name\n        FROM notes n\n        JOIN users u ON n.user_id = u.id\n        ORDER BY n.created_at DESC\n      `, (err, rows)=>{\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                resolve(rows);\n            });\n        }).catch(reject);\n    });\n}\nfunction getUserPasswords() {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            db.all(`\n        SELECT up.*, u.name as user_name\n        FROM user_passwords up\n        JOIN users u ON up.user_id = u.id\n        ORDER BY u.name\n      `, (err, rows)=>{\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                resolve(rows);\n            });\n        }).catch(reject);\n    });\n}\n// Clear all data except admin\nfunction clearDatabase() {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            db.serialize(()=>{\n                db.run(\"DELETE FROM notes\", (err)=>{\n                    if (err) {\n                        reject(err);\n                        return;\n                    }\n                    db.run('DELETE FROM user_passwords WHERE user_id != (SELECT id FROM users WHERE name = \"admin\")', (err)=>{\n                        if (err) {\n                            reject(err);\n                            return;\n                        }\n                        db.run('DELETE FROM roles WHERE role != \"admin\"', (err)=>{\n                            if (err) {\n                                reject(err);\n                                return;\n                            }\n                            db.run('DELETE FROM users WHERE name != \"admin\"', (err)=>{\n                                if (err) {\n                                    reject(err);\n                                    return;\n                                }\n                                resolve({\n                                    message: \"Database cleared successfully\"\n                                });\n                            });\n                        });\n                    });\n                });\n            });\n        }).catch(reject);\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/database.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/bcryptjs"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fusers%2Froute&page=%2Fapi%2Fadmin%2Fusers%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fusers%2Froute.ts&appDir=%2FUsers%2Fadityabalasubramanian%2FDesktop%2Fdev%2Fpoy%2Fpoy-app%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fadityabalasubramanian%2FDesktop%2Fdev%2Fpoy%2Fpoy-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();