# POY App - Simplified Version

A simplified web application for managing people and roles, built with Next.js and SQLite.

## Features

- **Simple Login System**: Admin (admin/zxcvbnm) and user authentication
- **SQLite Database**: Persistent storage with users and roles tables
- **Notion Integration**: Sync people data from Notion database
- **Role Management**: Automatic role assignment (detective, liar, normal) when 3+ users exist
- **Clean UI**: Simple dashboard for users, admin panel for management

## Quick Start

1. **Install dependencies**:
   ```bash
   npm install
   ```

2. **Set up environment variables** (create `.env.local`):
   ```
   NOTION_API_KEY=your_notion_api_key
   NOTION_DB_ID=your_notion_database_id
   ```

3. **Start the development server**:
   ```bash
   npm run dev
   ```

4. **Access the application**:
   - Open [http://localhost:3000](http://localhost:3000)
   - Login as admin: `admin` / `zxcvbnm`

## Database Schema

### Users Table
- `id`: Primary key
- `name`: User's name (unique)
- `email`: Email address
- `phone`: Phone number
- `about`: About text
- `selfie`: Selfie image URL
- `password`: Hashed password
- `created_at`: Creation timestamp

### Roles Table
- `id`: Primary key
- `user_id`: Foreign key to users table
- `role`: Role type (detective, liar, normal, admin)
- `assigned_at`: Role assignment timestamp

## API Endpoints

### Authentication
- `POST /api/login` - User login

### Data Management
- `GET /api/people` - Get all users with roles
- `POST /api/sync` - Sync data from Notion

### Admin Operations
- `POST /api/admin/clear` - Clear database (except admin)

## User Experience

### Admin Users
- Access to admin panel at `/admin`
- Can sync Notion data
- Can clear database
- View all users and role statistics

### Regular Users
- Access to dashboard at `/dashboard`
- See their own role and information
- Browse other users in a carousel interface
- Cannot access admin functions

## Role Assignment Logic

**Default Role**: All new users automatically receive the "Normal" role when created.

**Automatic Promotion**: When syncing from Notion, special roles are assigned:
- **5-8 users**: 1 Detective, 1 Liar, rest Normal
- **9+ users**: 2 Detectives, 2 Liars, rest Normal

**Sticky Roles**: Detective and Liar roles are permanent once assigned. New users always get Normal roles, and additional special roles are only assigned when reaching the 9-user threshold.

Roles are randomly shuffled and assigned to ensure fairness.

## Default Credentials

- **Admin**: `admin` / `zxcvbnm`
- **New Users**: Default password is `password123`

## Technical Details

- **Frontend**: Next.js 14 with React 18
- **Database**: SQLite with sqlite3 package
- **Authentication**: bcryptjs for password hashing
- **Styling**: Tailwind CSS
- **State Management**: Local storage for simple session management

## Development

The application is designed to be simple and easy to debug:
- No complex caching layers
- Direct SQL queries
- Minimal API endpoints
- Clear separation of concerns
- Simple UI components
