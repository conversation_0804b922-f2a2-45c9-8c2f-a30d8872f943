import { NextRequest, NextResponse } from 'next/server'
import { clearDatabase } from '@/lib/database'

export async function POST(request: NextRequest) {
  try {
    const result = await clearDatabase()

    return NextResponse.json({
      success: true,
      message: result.message
    })

  } catch (error) {
    console.error('Clear database error:', error)
    return NextResponse.json(
      { success: false, message: 'Failed to clear database' },
      { status: 500 }
    )
  }
}
