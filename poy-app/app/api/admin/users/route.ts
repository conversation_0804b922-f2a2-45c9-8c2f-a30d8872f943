import { NextRequest, NextResponse } from 'next/server'
import { getAllUsersWithRoles, getUserPasswords } from '@/lib/database'

export async function GET(request: NextRequest) {
  try {
    // Get all users with their roles and actual passwords (admin only endpoint)
    const users = await getAllUsersWithRoles()
    const passwords = await getUserPasswords()

    // Create a map of user_id to password
    const passwordMap = new Map()
    passwords.forEach(p => {
      passwordMap.set(p.user_id, p.plain_password)
    })

    // Add actual passwords to users
    const usersWithPasswords = users.map((user: any) => {
      let displayPassword = 'Unknown'

      // Get actual password from database or use default for admin
      if (user.name === 'admin') {
        displayPassword = 'zxcvbnm'
      } else {
        displayPassword = passwordMap.get(user.id) || 'Not found'
      }

      return {
        ...user,
        displayPassword // Add display password field
      }
    })

    return NextResponse.json({
      success: true,
      users: usersWithPasswords
    })

  } catch (error) {
    console.error('Get admin users error:', error)
    return NextResponse.json(
      { success: false, message: 'Failed to get users with passwords' },
      { status: 500 }
    )
  }
}
