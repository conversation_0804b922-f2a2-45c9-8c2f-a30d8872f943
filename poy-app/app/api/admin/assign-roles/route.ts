import { NextRequest, NextResponse } from 'next/server'
import { assignRolesToNonAdmins } from '@/lib/database'

export async function POST(request: NextRequest) {
  try {
    const result = await assignRolesToNonAdmins()

    return NextResponse.json({
      success: true,
      message: result.message
    })

  } catch (error) {
    console.error('Assign roles error:', error)
    return NextResponse.json(
      { success: false, message: 'Failed to assign roles' },
      { status: 500 }
    )
  }
}
