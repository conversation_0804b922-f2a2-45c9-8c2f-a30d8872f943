import { NextRequest, NextResponse } from 'next/server'
import { createUser } from '@/lib/database'

// Arrays for generating random data
const firstNames = [
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
  '<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>',
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON>'
]

const lastNames = [
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'
]

const bioTemplates = [
  "I'm a {adjective} {profession} who loves {hobby} and {food}. Always looking for new adventures!",
  "Passionate about {hobby} and {interest}. {profession} by day, {hobby2} enthusiast by night.",
  "Life is about {philosophy}. I enjoy {activity} and discovering new {interest}.",
  "{profession} with a love for {hobby}. Coffee addict and {adjective} person.",
  "Explorer of {interest} and {hobby}. Believer in {philosophy} and good {food}.",
  "Creative {profession} who finds joy in {activity} and {hobby2}. {adjective} and curious!",
  "{adjective} soul with a passion for {interest}. Love {activity} and meeting new people.",
  "Professional {profession} and amateur {hobby} enthusiast. Always {adjective} and ready to learn."
]

const adjectives = ['creative', 'adventurous', 'curious', 'passionate', 'optimistic', 'friendly', 'innovative', 'thoughtful']
const professions = ['designer', 'developer', 'teacher', 'artist', 'writer', 'consultant', 'engineer', 'researcher']
const hobbies = ['photography', 'hiking', 'cooking', 'reading', 'gaming', 'traveling', 'music', 'dancing']
const hobbies2 = ['yoga', 'cycling', 'painting', 'gardening', 'coding', 'writing', 'running', 'swimming']
const interests = ['technology', 'nature', 'culture', 'science', 'art', 'history', 'psychology', 'philosophy']
const activities = ['exploring new places', 'trying new foods', 'learning new skills', 'meeting people', 'solving problems']
const philosophies = ['living in the moment', 'continuous learning', 'making a difference', 'staying positive', 'embracing change']
const foods = ['pizza', 'sushi', 'tacos', 'pasta', 'burgers', 'salads', 'ice cream', 'coffee']

function getRandomElement<T>(array: T[]): T {
  return array[Math.floor(Math.random() * array.length)]
}

function generateRandomName(): string {
  const firstName = getRandomElement(firstNames)
  const lastName = getRandomElement(lastNames)
  return `${firstName} ${lastName}`
}

function generateRandomEmail(name: string): string {
  const domains = ['gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com', 'example.com']
  const cleanName = name.toLowerCase().replace(/\s+/g, '.')
  const randomNum = Math.floor(Math.random() * 999) + 1
  const domain = getRandomElement(domains)
  return `${cleanName}${randomNum}@${domain}`
}

function generateRandomPhone(): string {
  // Generate a US phone number format: +1-XXX-XXX-XXXX
  const areaCode = Math.floor(Math.random() * 900) + 100 // 100-999
  const exchange = Math.floor(Math.random() * 900) + 100 // 100-999
  const number = Math.floor(Math.random() * 9000) + 1000 // 1000-9999
  return `+1-${areaCode}-${exchange}-${number}`
}

function generateRandomBio(): string {
  const template = getRandomElement(bioTemplates)
  return template
    .replace('{adjective}', getRandomElement(adjectives))
    .replace('{profession}', getRandomElement(professions))
    .replace('{hobby}', getRandomElement(hobbies))
    .replace('{hobby2}', getRandomElement(hobbies2))
    .replace('{interest}', getRandomElement(interests))
    .replace('{activity}', getRandomElement(activities))
    .replace('{philosophy}', getRandomElement(philosophies))
    .replace('{food}', getRandomElement(foods))
}

export async function POST(request: NextRequest) {
  try {
    const name = generateRandomName()
    const email = generateRandomEmail(name)
    const phone = generateRandomPhone()
    const bio = generateRandomBio()

    const result = await createUser(name, email, phone, bio, '')

    return NextResponse.json({
      success: true,
      message: `Dummy user "${name}" created successfully`,
      user: {
        id: result.lastID,
        name,
        email,
        phone,
        bio,
        password: result.password
      }
    })

  } catch (error: any) {
    console.error('Generate dummy user error:', error)
    
    if (error.code === 'SQLITE_CONSTRAINT') {
      return NextResponse.json(
        { success: false, message: 'Generated user conflicts with existing data. Try again.' },
        { status: 409 }
      )
    }

    return NextResponse.json(
      { success: false, message: 'Failed to generate dummy user' },
      { status: 500 }
    )
  }
}
