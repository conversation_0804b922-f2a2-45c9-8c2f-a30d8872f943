import { NextRequest, NextResponse } from 'next/server'
import { createNote, getAllNotes, getUserByName } from '@/lib/database'

// Submit a note (for regular users)
export async function POST(request: NextRequest) {
  try {
    const { userName, content } = await request.json()

    if (!userName || !content) {
      return NextResponse.json(
        { success: false, message: 'User name and content are required' },
        { status: 400 }
      )
    }

    if (content.trim().length === 0) {
      return NextResponse.json(
        { success: false, message: 'Note content cannot be empty' },
        { status: 400 }
      )
    }

    if (content.length > 1000) {
      return NextResponse.json(
        { success: false, message: 'Note content is too long (max 1000 characters)' },
        { status: 400 }
      )
    }

    // Get user by name to get user ID
    const user = await getUserByName(userName)
    if (!user) {
      return NextResponse.json(
        { success: false, message: 'User not found' },
        { status: 404 }
      )
    }

    // Create the note
    const result = await createNote(user.id, content.trim())

    return NextResponse.json({
      success: true,
      message: 'Note submitted successfully',
      noteId: result.lastID
    })

  } catch (error) {
    console.error('Submit note error:', error)
    return NextResponse.json(
      { success: false, message: 'Failed to submit note' },
      { status: 500 }
    )
  }
}

// Get all notes (admin only)
export async function GET(request: NextRequest) {
  try {
    const notes = await getAllNotes()

    return NextResponse.json({
      success: true,
      notes: notes
    })

  } catch (error) {
    console.error('Get notes error:', error)
    return NextResponse.json(
      { success: false, message: 'Failed to get notes' },
      { status: 500 }
    )
  }
}
