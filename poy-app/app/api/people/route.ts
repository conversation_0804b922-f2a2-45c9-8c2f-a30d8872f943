import { NextRequest, NextResponse } from 'next/server'
import { getAllUsersWithRoles } from '@/lib/database'

export async function GET(request: NextRequest) {
  try {
    const users = await getAllUsersWithRoles()

    // Remove passwords from response
    const usersWithoutPasswords = users.map((user: any) => {
      const { password, ...userWithoutPassword } = user
      return userWithoutPassword
    })

    return NextResponse.json({
      success: true,
      users: usersWithoutPasswords
    })

  } catch (error) {
    console.error('Get people error:', error)
    return NextResponse.json(
      { success: false, message: 'Failed to get users' },
      { status: 500 }
    )
  }
}
