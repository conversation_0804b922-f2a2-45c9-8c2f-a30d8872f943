import { NextRequest, NextResponse } from 'next/server'
import { fetchNotionDatabase } from '@/lib/notion'
import { createUser, userExists, getNonAdminUserCount, assignStickyRolesToNonAdmins } from '@/lib/database'

export async function POST(request: NextRequest) {
  try {
    // Fetch from Notion
    const notionPeople = await fetchNotionDatabase()

    // Create users in database (only new ones)
    const createdUsers = []
    const skippedUsers = []

    for (const person of notionPeople) {
      try {
        // Check if user already exists
        const exists = await userExists(person.name, person.email)

        if (exists) {
          console.log(`Skipping existing user: ${person.name} (${person.email})`)
          skippedUsers.push({
            name: person.name,
            email: person.email,
            reason: 'User already exists'
          })
          continue
        }

        const selfieUrl = person.selfie && person.selfie.length > 0 ? person.selfie[0] : null
        const result = await createUser(
          person.name,
          person.email,
          person.phone,
          person.about,
          selfieUrl || ''
        )
        createdUsers.push({
          name: person.name,
          id: result.lastID,
          password: result.password
        })
      } catch (error: any) {
        if (error.code === 'SQLITE_CONSTRAINT') {
          console.log(`Skipping duplicate user: ${person.name} (${person.email})`)
          skippedUsers.push({
            name: person.name,
            email: person.email,
            reason: 'Duplicate name or email'
          })
        } else {
          console.error(`Error creating user ${person.name}:`, error)
          skippedUsers.push({
            name: person.name,
            email: person.email,
            reason: error.message
          })
        }
      }
    }

    // Check if we have enough users to assign roles automatically
    const nonAdminCount = await getNonAdminUserCount()
    let roleAssignmentMessage = ''

    if (nonAdminCount >= 5) {
      try {
        const roleResult = await assignStickyRolesToNonAdmins()
        roleAssignmentMessage = ` | ${roleResult.message}`
      } catch (error) {
        console.error('Error assigning roles after sync:', error)
        roleAssignmentMessage = ' | Failed to assign roles automatically'
      }
    } else {
      roleAssignmentMessage = ` | Need ${5 - nonAdminCount} more non-admin users for automatic role assignment`
    }

    return NextResponse.json({
      success: true,
      message: `Added ${createdUsers.length} new users from Notion${skippedUsers.length > 0 ? `, skipped ${skippedUsers.length} existing users` : ''}${roleAssignmentMessage}`,
      users: createdUsers,
      skipped: skippedUsers,
      nonAdminUserCount: nonAdminCount,
      rolesAssigned: nonAdminCount >= 5
    })

  } catch (error) {
    console.error('Sync error:', error)
    return NextResponse.json(
      { success: false, message: 'Failed to sync with Notion' },
      { status: 500 }
    )
  }
}
