'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'

interface User {
  id: number
  name: string
  email: string | null
  phone: string | null
  about: string | null
  selfie: string | null
  role: 'detective' | 'liar' | 'normal' | 'admin'
  displayPassword?: string
}

export default function AdminPage() {
  const [user, setUser] = useState<User | null>(null)
  const [allUsers, setAllUsers] = useState<User[]>([])
  const [loading, setLoading] = useState(true)
  const [syncing, setSyncing] = useState(false)
  const [message, setMessage] = useState('')
  const [copiedPassword, setCopiedPassword] = useState<string | null>(null)
  const [notes, setNotes] = useState<any[]>([])
  const [showNotes, setShowNotes] = useState(false)
  const router = useRouter()

  useEffect(() => {
    // Check if user is logged in and is admin
    const userData = localStorage.getItem('user')
    if (!userData) {
      router.push('/')
      return
    }

    const userObj = JSON.parse(userData)
    if (userObj.role !== 'admin') {
      router.push('/dashboard')
      return
    }

    setUser(userObj)
    fetchUsers()
    fetchNotes()
  }, [router])

  const fetchUsers = async () => {
    try {
      const response = await fetch('/api/admin/users')
      const data = await response.json()

      if (data.success) {
        setAllUsers(data.users)
      }
    } catch (error) {
      console.error('Failed to fetch users:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchNotes = async () => {
    try {
      const response = await fetch('/api/notes')
      const data = await response.json()

      if (data.success) {
        setNotes(data.notes)
      }
    } catch (error) {
      console.error('Failed to fetch notes:', error)
    }
  }

  const handleSync = async () => {
    setSyncing(true)
    setMessage('')
    
    try {
      const response = await fetch('/api/sync', { method: 'POST' })
      const data = await response.json()
      
      if (data.success) {
        setMessage(data.message)
        fetchUsers() // Refresh the user list
        fetchNotes() // Refresh notes
      } else {
        setMessage(data.message || 'Sync failed')
      }
    } catch (error) {
      setMessage('Network error during sync')
    } finally {
      setSyncing(false)
    }
  }

  const handleClearDB = async () => {
    if (!confirm('Are you sure you want to clear the database? This will remove all users except admin.')) {
      return
    }
    
    try {
      const response = await fetch('/api/admin/clear', { method: 'POST' })
      const data = await response.json()
      
      if (data.success) {
        setMessage(data.message)
        fetchUsers() // Refresh the user list
      } else {
        setMessage(data.message || 'Clear failed')
      }
    } catch (error) {
      setMessage('Network error during clear')
    }
  }

  const handleAssignRoles = async () => {
    try {
      const response = await fetch('/api/admin/assign-roles', { method: 'POST' })
      const data = await response.json()
      
      if (data.success) {
        setMessage(data.message)
        fetchUsers() // Refresh the user list
      } else {
        setMessage(data.message || 'Role assignment failed')
      }
    } catch (error) {
      setMessage('Network error during role assignment')
    }
  }

  const handleLogout = () => {
    localStorage.removeItem('user')
    router.push('/')
  }

  const copyPassword = async (password: string, userName: string) => {
    try {
      await navigator.clipboard.writeText(password)
      setCopiedPassword(userName)
      setTimeout(() => setCopiedPassword(null), 2000) // Clear after 2 seconds
    } catch (error) {
      console.error('Failed to copy password:', error)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  if (!user) return null

  const nonAdminUsers = allUsers.filter(u => u.role !== 'admin')
  const roleStats = {
    detective: nonAdminUsers.filter(u => u.role === 'detective').length,
    liar: nonAdminUsers.filter(u => u.role === 'liar').length,
    normal: nonAdminUsers.filter(u => u.role === 'normal').length,
    unassigned: nonAdminUsers.filter(u => !u.role).length
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-6xl mx-auto py-8 px-4">
        {/* Header */}
        <div className="bg-white rounded-lg shadow p-6 mb-8">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Admin Panel</h1>
              <p className="text-gray-600">Welcome, {user.name}</p>
            </div>
            <button
              onClick={handleLogout}
              className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
            >
              Logout
            </button>
          </div>
        </div>

        {/* Password Information */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-6">
          <h3 className="text-sm font-medium text-yellow-800 mb-2">Password Information:</h3>
          <div className="text-sm text-yellow-700 space-y-1">
            <div className="flex items-center space-x-2">
              <span>Admin user:</span>
              <span className="font-mono bg-yellow-100 px-2 py-1 rounded">zxcvbnm</span>
              <button
                onClick={() => copyPassword('zxcvbnm', 'admin-default')}
                className="text-yellow-600 hover:text-yellow-800 text-xs"
              >
                {copiedPassword === 'admin-default' ? '✓ Copied!' : '📋 Copy'}
              </button>
            </div>
            <div className="flex items-center space-x-2">
              <span>All other users:</span>
              <span className="font-mono bg-yellow-100 px-2 py-1 rounded">Random 8-character passwords</span>
              <span className="text-xs text-yellow-600">(see table below for individual passwords)</span>
            </div>
          </div>
        </div>

        {/* Message */}
        {message && (
          <div className="bg-blue-50 border border-blue-200 rounded-md p-4 mb-6">
            <p className="text-blue-800">{message}</p>
          </div>
        )}

        {/* Actions */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <button
            onClick={handleSync}
            disabled={syncing}
            className="bg-blue-600 text-white p-4 rounded-lg hover:bg-blue-700 disabled:opacity-50"
          >
            {syncing ? 'Syncing...' : 'Sync Notion'}
          </button>

          <button
            onClick={handleClearDB}
            className="bg-red-600 text-white p-4 rounded-lg hover:bg-red-700"
          >
            Clear Database
          </button>

          <button
            onClick={handleAssignRoles}
            className="bg-green-600 text-white p-4 rounded-lg hover:bg-green-700"
          >
            Assign Roles
          </button>

          <button
            onClick={() => setShowNotes(!showNotes)}
            className="bg-purple-600 text-white p-4 rounded-lg hover:bg-purple-700"
          >
            {showNotes ? 'Hide Notes' : `View Notes (${notes.length})`}
          </button>
        </div>

        {/* Stats */}
        <div className="bg-white rounded-lg shadow p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Role Statistics</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{roleStats.detective}</div>
              <div className="text-sm text-gray-600">Detective</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">{roleStats.liar}</div>
              <div className="text-sm text-gray-600">Liar</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{roleStats.normal}</div>
              <div className="text-sm text-gray-600">Normal</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-600">{roleStats.unassigned}</div>
              <div className="text-sm text-gray-600">Unassigned</div>
            </div>
          </div>
        </div>

        {/* Notes Section */}
        {showNotes && (
          <div className="bg-white rounded-lg shadow p-6 mb-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">User Notes ({notes.length})</h2>
            {notes.length === 0 ? (
              <p className="text-gray-500 text-center py-8">No notes submitted yet.</p>
            ) : (
              <div className="space-y-4 max-h-96 overflow-y-auto">
                {notes.map((note) => (
                  <div key={note.id} className="border border-gray-200 rounded-lg p-4 bg-gray-50">
                    <div className="flex justify-between items-start mb-2">
                      <span className="font-semibold text-gray-900">{note.user_name}</span>
                      <span className="text-sm text-gray-500">
                        {new Date(note.created_at).toLocaleString()}
                      </span>
                    </div>
                    <p className="text-gray-700 whitespace-pre-wrap">{note.content}</p>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {/* Users List */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">All Users ({allUsers.length})</h2>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Password</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {allUsers.map((user) => (
                  <tr key={user.id}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{user.name}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                        user.role === 'admin' ? 'bg-purple-100 text-purple-800' :
                        user.role === 'detective' ? 'bg-blue-100 text-blue-800' :
                        user.role === 'liar' ? 'bg-red-100 text-red-800' :
                        user.role === 'normal' ? 'bg-green-100 text-green-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {user.role || 'Unassigned'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{user.email || '-'}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <div className="flex items-center space-x-2">
                        <span className="font-mono bg-gray-100 px-2 py-1 rounded text-xs">
                          {user.displayPassword || 'Unknown'}
                        </span>
                        <button
                          onClick={() => copyPassword(user.displayPassword || '', user.name)}
                          className="text-blue-600 hover:text-blue-800 text-xs"
                          title="Copy password"
                        >
                          {copiedPassword === user.name ? '✓ Copied!' : '📋 Copy'}
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  )
}
